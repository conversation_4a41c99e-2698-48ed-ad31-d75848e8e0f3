#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图表查看器 - 验证中文字体和数学符号显示效果
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import os
from PIL import Image
import numpy as np

def setup_font():
    """设置字体"""
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['mathtext.fontset'] = 'stix'
    plt.rcParams['mathtext.default'] = 'regular'

def show_sample_images():
    """显示示例图片"""
    setup_font()
    
    # 检查图片文件
    base_dir = "问题1_室内温度波动规律分析"
    
    sample_images = [
        "1.2_温度相关性分析/地点1_温度相关性散点图.png",
        "1.3_能耗温差分析/地点1_能耗温差散点图.png", 
        "1.4_变量相关性分析/地点1_变量相关性热力图.png"
    ]
    
    print("检查图片文件...")
    for img_path in sample_images:
        full_path = os.path.join(base_dir, img_path)
        if os.path.exists(full_path):
            print(f"✓ {img_path} - 存在")
        else:
            print(f"✗ {img_path} - 不存在")
    
    # 创建一个新的测试图表来验证字体
    print("\n创建字体验证图表...")
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 测试1：中文标题和标签
    x = np.linspace(0, 10, 50)
    y = 2 * x + 1 + np.random.normal(0, 1, 50)
    
    axes[0,0].scatter(x, y, alpha=0.6, color='blue', label='数据点')
    axes[0,0].plot(x, 2*x + 1, 'r-', linewidth=2, label='回归线 ($R^2$=0.85)')
    axes[0,0].set_xlabel('环境温度 (℃)', fontsize=12)
    axes[0,0].set_ylabel('室内温度 (℃)', fontsize=12)
    axes[0,0].set_title('中文字体测试 - 温度相关性分析', fontsize=14)
    axes[0,0].legend()
    axes[0,0].grid(True, alpha=0.3)
    
    # 测试2：数学符号
    x2 = np.linspace(-3, 3, 100)
    y2 = x2**2
    
    axes[0,1].plot(x2, y2, 'g-', linewidth=2, label='$y = x^2$')
    axes[0,1].set_xlabel('温差 (℃)', fontsize=12)
    axes[0,1].set_ylabel('功率 (kW)', fontsize=12)
    axes[0,1].set_title('数学符号测试 - $R^2$, $\\alpha$, $\\beta$', fontsize=14)
    axes[0,1].legend()
    axes[0,1].grid(True, alpha=0.3)
    
    # 测试3：热力图样式
    data = np.random.rand(5, 5)
    im = axes[1,0].imshow(data, cmap='RdBu_r')
    axes[1,0].set_title('热力图测试 - 变量相关性', fontsize=14)
    axes[1,0].set_xlabel('变量1', fontsize=12)
    axes[1,0].set_ylabel('变量2', fontsize=12)
    
    # 添加颜色条
    cbar = plt.colorbar(im, ax=axes[1,0])
    cbar.set_label('相关系数', fontsize=10)
    
    # 测试4：统计信息文本
    axes[1,1].text(0.1, 0.8, '统计信息测试:', fontsize=14, weight='bold')
    axes[1,1].text(0.1, 0.7, '平均温度: 20.48℃', fontsize=12)
    axes[1,1].text(0.1, 0.6, '标准差: 1.01℃', fontsize=12)
    axes[1,1].text(0.1, 0.5, '相关系数: 0.856', fontsize=12)
    axes[1,1].text(0.1, 0.4, '回归方程: y = 0.123x + 2.456', fontsize=12)
    axes[1,1].text(0.1, 0.3, '$R^2$ = 0.850', fontsize=12)
    axes[1,1].text(0.1, 0.2, '数学公式: $\\alpha = \\frac{\\sum x_i}{n}$', fontsize=12)
    axes[1,1].set_xlim(0, 1)
    axes[1,1].set_ylim(0, 1)
    axes[1,1].set_title('文本显示测试', fontsize=14)
    axes[1,1].axis('off')
    
    plt.tight_layout()
    plt.savefig('字体修复验证图.png', dpi=300, bbox_inches='tight')
    print("验证图表已保存为: 字体修复验证图.png")
    
    # 显示图表
    plt.show()
    
    print("\n" + "=" * 60)
    print("字体修复验证完成！")
    print("请检查生成的图表中：")
    print("1. 中文字符是否正常显示（不是方框）")
    print("2. R²符号是否正确显示为上标")
    print("3. 数学公式是否正确渲染")
    print("=" * 60)

def check_generated_files():
    """检查生成的文件"""
    base_dir = "问题1_室内温度波动规律分析"
    
    if not os.path.exists(base_dir):
        print(f"错误: 找不到目录 {base_dir}")
        return
    
    print(f"\n检查目录: {base_dir}")
    print("-" * 50)
    
    for root, dirs, files in os.walk(base_dir):
        level = root.replace(base_dir, '').count(os.sep)
        indent = ' ' * 2 * level
        print(f"{indent}{os.path.basename(root)}/")
        
        subindent = ' ' * 2 * (level + 1)
        for file in files:
            file_path = os.path.join(root, file)
            file_size = os.path.getsize(file_path)
            print(f"{subindent}{file} ({file_size} bytes)")

if __name__ == "__main__":
    print("图表查看器 - 验证字体修复效果")
    print("=" * 60)
    
    # 检查生成的文件
    check_generated_files()
    
    # 显示示例图表
    show_sample_images()
