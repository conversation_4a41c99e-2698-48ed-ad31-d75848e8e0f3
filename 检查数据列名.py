#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据文件的列名和基本信息
"""

import pandas as pd

def check_data_columns():
    """检查数据列名"""
    print("检查数据文件列名...")
    
    # 读取地点1数据
    print("\n=== 地点1数据 ===")
    try:
        data1 = pd.read_csv('data/附件2/地点1/地点1_完整合并表.csv', encoding='utf-8')
        print(f"数据形状: {data1.shape}")
        print("列名:")
        for i, col in enumerate(data1.columns):
            print(f"  {i+1:2d}. {col}")
        
        print("\n前5行数据:")
        print(data1.head())
        
        print("\n数据类型:")
        print(data1.dtypes)
        
    except Exception as e:
        print(f"读取地点1数据失败: {e}")
    
    # 读取地点2数据
    print("\n=== 地点2数据 ===")
    try:
        data2 = pd.read_csv('data/附件2/地点2/地点2_完整合并表.csv', encoding='utf-8')
        print(f"数据形状: {data2.shape}")
        print("列名:")
        for i, col in enumerate(data2.columns):
            print(f"  {i+1:2d}. {col}")
            
        print("\n前5行数据:")
        print(data2.head())
        
        print("\n数据类型:")
        print(data2.dtypes)
        
    except Exception as e:
        print(f"读取地点2数据失败: {e}")

if __name__ == "__main__":
    check_data_columns()
