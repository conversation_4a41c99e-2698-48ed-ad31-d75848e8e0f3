#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
空气源热泵数据分析 - 问题一
分析室内温度波动规律、室内外温度相关性、热泵能耗与温差关系以及影响因素
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score
import warnings
import os
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class HeatPumpAnalyzer:
    def __init__(self):
        self.data_location1 = None
        self.data_location2 = None

        # 创建子问题文件夹
        import os
        self.folders = [
            "1-室内温度波动规律分析",
            "2-室内外温度相关性分析",
            "3-热泵能耗与温差定量关系分析",
            "4-影响室内温度的影响因素分析"
        ]

        for folder in self.folders:
            if not os.path.exists(folder):
                os.makedirs(folder)
                print(f"创建文件夹: {folder}")

    def load_data(self):
        """加载数据"""
        print("正在加载数据...")
        try:
            # 读取地点1数据
            self.data_location1 = pd.read_csv('../data/附件2/地点1/地点1_完整合并表.csv', encoding='utf-8')
            print(f"地点1数据加载成功，共{len(self.data_location1)}行")

            # 读取地点2数据
            self.data_location2 = pd.read_csv('../data/附件2/地点2/地点2_完整合并表.csv', encoding='utf-8')
            print(f"地点2数据加载成功，共{len(self.data_location2)}行")

        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
        return True

    def preprocess_data(self):
        """数据预处理"""
        print("正在进行数据预处理...")

        for i, (data, location) in enumerate([(self.data_location1, "地点1"), (self.data_location2, "地点2")]):
            print(f"\n处理{location}数据:")

            # 转换时间列
            data['时间'] = pd.to_datetime(data['时间'])

            # 提取关键列 - 使用正确的列名
            key_columns = ['时间', '环境温度(℃)', '室内温度均值', '热泵功率(kw)', '设定温度(℃)']

            # 检查列是否存在
            existing_columns = [col for col in key_columns if col in data.columns]
            print(f"可用列: {existing_columns}")

            # 数据清洗
            # 处理异常值和缺失值
            if '室内温度均值' in data.columns:
                # 移除明显异常的温度值
                data = data[(data['室内温度均值'] >= 10) & (data['室内温度均值'] <= 35)]

            if '环境温度(℃)' in data.columns:
                data = data[(data['环境温度(℃)'] >= -20) & (data['环境温度(℃)'] <= 40)]

            # 添加时间特征
            data['小时'] = data['时间'].dt.hour
            data['日期'] = data['时间'].dt.date
            data['月份'] = data['时间'].dt.month
            data['星期'] = data['时间'].dt.dayofweek

            # 计算温差
            if '室内温度均值' in data.columns and '环境温度(℃)' in data.columns:
                data['室内外温差'] = data['室内温度均值'] - data['环境温度(℃)']

            # 更新数据
            if i == 0:
                self.data_location1 = data
            else:
                self.data_location2 = data

            print(f"{location}预处理完成，剩余{len(data)}行数据")

    def analyze_temperature_fluctuation(self):
        """分析室内温度波动规律"""
        print("\n=== 室内温度波动规律分析 ===")

        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('室内温度波动规律分析', fontsize=16, fontweight='bold')

        for i, (data, location) in enumerate([(self.data_location1, "地点1"), (self.data_location2, "地点2")]):
            if '室内温度均值' not in data.columns:
                continue

            # 1. 日内温度变化规律
            hourly_temp = data.groupby('小时')['室内温度均值'].agg(['mean', 'std', 'min', 'max'])

            axes[i, 0].plot(hourly_temp.index, hourly_temp['mean'], 'o-', linewidth=2, markersize=4)
            axes[i, 0].fill_between(hourly_temp.index,
                                   hourly_temp['mean'] - hourly_temp['std'],
                                   hourly_temp['mean'] + hourly_temp['std'],
                                   alpha=0.3)
            axes[i, 0].set_title(f'{location} - 日内温度变化')
            axes[i, 0].set_xlabel('小时')
            axes[i, 0].set_ylabel('室内温度 (℃)')
            axes[i, 0].grid(True, alpha=0.3)

            # 2. 温度分布直方图
            axes[i, 1].hist(data['室内温度均值'].dropna(), bins=30, alpha=0.7, density=True)
            axes[i, 1].axvline(data['室内温度均值'].mean(), color='red', linestyle='--',
                              label=f'均值: {data["室内温度均值"].mean():.1f}℃')
            axes[i, 1].axvline(20, color='green', linestyle='--', alpha=0.7, label='目标温度: 20℃')
            axes[i, 1].set_title(f'{location} - 温度分布')
            axes[i, 1].set_xlabel('室内温度 (℃)')
            axes[i, 1].set_ylabel('密度')
            axes[i, 1].legend()
            axes[i, 1].grid(True, alpha=0.3)

            # 3. 时间序列趋势
            sample_data = data.iloc[::24]  # 每天采样一次
            axes[i, 2].plot(sample_data['时间'], sample_data['室内温度均值'], alpha=0.7)
            axes[i, 2].set_title(f'{location} - 温度时间序列')
            axes[i, 2].set_xlabel('时间')
            axes[i, 2].set_ylabel('室内温度 (℃)')
            axes[i, 2].tick_params(axis='x', rotation=45)
            axes[i, 2].grid(True, alpha=0.3)

            # 统计信息
            temp_stats = data['室内温度均值'].describe()
            print(f"\n{location}室内温度统计:")
            print(f"  均值: {temp_stats['mean']:.2f}℃")
            print(f"  标准差: {temp_stats['std']:.2f}℃")
            print(f"  最小值: {temp_stats['min']:.2f}℃")
            print(f"  最大值: {temp_stats['max']:.2f}℃")
            print(f"  温度范围: {temp_stats['max'] - temp_stats['min']:.2f}℃")

            # 舒适度分析
            comfort_range = (data['室内温度均值'] >= 19) & (data['室内温度均值'] <= 21)
            comfort_rate = comfort_range.sum() / len(data) * 100
            print(f"  舒适度(19-21℃)达标率: {comfort_rate:.1f}%")

        plt.tight_layout()

        # 保存图片到对应文件夹
        save_path = os.path.join(self.folders[0], '室内温度波动规律分析.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

        # 生成详细说明文件
        self._generate_fluctuation_report()

    def analyze_indoor_outdoor_correlation(self):
        """分析室内外温度相关性"""
        print("\n=== 室内外温度相关性分析 ===")

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('室内外温度相关性分析', fontsize=16, fontweight='bold')

        for i, (data, location) in enumerate([(self.data_location1, "地点1"), (self.data_location2, "地点2")]):
            if '室内温度均值' not in data.columns or '环境温度(℃)' not in data.columns:
                continue

            # 清理数据
            clean_data = data[['室内温度均值', '环境温度(℃)']].dropna()

            if len(clean_data) == 0:
                continue

            # 1. 散点图和回归线
            axes[i, 0].scatter(clean_data['环境温度(℃)'], clean_data['室内温度均值'],
                              alpha=0.5, s=10)

            # 计算回归线
            z = np.polyfit(clean_data['环境温度(℃)'], clean_data['室内温度均值'], 1)
            p = np.poly1d(z)
            x_range = np.linspace(clean_data['环境温度(℃)'].min(), clean_data['环境温度(℃)'].max(), 100)
            axes[i, 0].plot(x_range, p(x_range), "r--", alpha=0.8, linewidth=2)

            # 计算相关系数
            correlation = clean_data['环境温度(℃)'].corr(clean_data['室内温度均值'])

            axes[i, 0].set_title(f'{location} - 相关性 (r={correlation:.3f})')
            axes[i, 0].set_xlabel('环境温度 (℃)')
            axes[i, 0].set_ylabel('室内温度 (℃)')
            axes[i, 0].grid(True, alpha=0.3)

            # 2. 温差时间序列
            if '室内外温差' in data.columns:
                sample_data = data.iloc[::24]  # 每天采样一次
                axes[i, 1].plot(sample_data['时间'], sample_data['室内外温差'], alpha=0.7)
                axes[i, 1].axhline(y=0, color='red', linestyle='--', alpha=0.5)
                axes[i, 1].set_title(f'{location} - 室内外温差变化')
                axes[i, 1].set_xlabel('时间')
                axes[i, 1].set_ylabel('温差 (℃)')
                axes[i, 1].tick_params(axis='x', rotation=45)
                axes[i, 1].grid(True, alpha=0.3)

            print(f"\n{location}相关性分析:")
            print(f"  相关系数: {correlation:.3f}")

            if abs(correlation) > 0.7:
                print("  相关性: 强相关")
            elif abs(correlation) > 0.3:
                print("  相关性: 中等相关")
            else:
                print("  相关性: 弱相关")

        plt.tight_layout()

        # 保存图片到对应文件夹
        save_path = os.path.join(self.folders[1], '室内外温度相关性分析.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

        # 生成详细说明文件
        self._generate_correlation_report()

    def analyze_energy_temperature_relationship(self):
        """分析热泵能耗与温差的定量关系"""
        print("\n=== 热泵能耗与温差关系分析 ===")

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('热泵能耗与温差关系分析', fontsize=16, fontweight='bold')

        for i, (data, location) in enumerate([(self.data_location1, "地点1"), (self.data_location2, "地点2")]):
            if '热泵功率(kw)' not in data.columns or '室内外温差' not in data.columns:
                continue

            # 清理数据
            clean_data = data[['热泵功率(kw)', '室内外温差', '环境温度(℃)']].dropna()
            clean_data = clean_data[clean_data['热泵功率(kw)'] >= 0]  # 移除负功率值

            if len(clean_data) == 0:
                continue

            # 1. 功率与温差散点图
            axes[i, 0].scatter(clean_data['室内外温差'], clean_data['热泵功率(kw)'],
                              alpha=0.6, s=15)

            # 拟合回归线
            if len(clean_data) > 10:
                z = np.polyfit(clean_data['室内外温差'], clean_data['热泵功率(kw)'], 1)
                p = np.poly1d(z)
                x_range = np.linspace(clean_data['室内外温差'].min(),
                                    clean_data['室内外温差'].max(), 100)
                axes[i, 0].plot(x_range, p(x_range), "r--", alpha=0.8, linewidth=2)

                # 计算相关系数
                correlation = clean_data['室内外温差'].corr(clean_data['热泵功率(kw)'])
                axes[i, 0].set_title(f'{location} - 功率vs温差 (r={correlation:.3f})')
            else:
                axes[i, 0].set_title(f'{location} - 功率vs温差')

            axes[i, 0].set_xlabel('室内外温差 (℃)')
            axes[i, 0].set_ylabel('热泵功率 (kW)')
            axes[i, 0].grid(True, alpha=0.3)

            # 2. 功率与环境温度关系
            axes[i, 1].scatter(clean_data['环境温度(℃)'], clean_data['热泵功率(kw)'],
                              alpha=0.6, s=15, color='orange')

            if len(clean_data) > 10:
                z2 = np.polyfit(clean_data['环境温度(℃)'], clean_data['热泵功率(kw)'], 1)
                p2 = np.poly1d(z2)
                x_range2 = np.linspace(clean_data['环境温度(℃)'].min(),
                                     clean_data['环境温度(℃)'].max(), 100)
                axes[i, 1].plot(x_range2, p2(x_range2), "r--", alpha=0.8, linewidth=2)

                correlation2 = clean_data['环境温度(℃)'].corr(clean_data['热泵功率(kw)'])
                axes[i, 1].set_title(f'{location} - 功率vs环境温度 (r={correlation2:.3f})')
            else:
                axes[i, 1].set_title(f'{location} - 功率vs环境温度')

            axes[i, 1].set_xlabel('环境温度 (℃)')
            axes[i, 1].set_ylabel('热泵功率 (kW)')
            axes[i, 1].grid(True, alpha=0.3)

            # 统计分析
            power_stats = clean_data['热泵功率(kw)'].describe()
            print(f"\n{location}热泵功率统计:")
            print(f"  平均功率: {power_stats['mean']:.2f} kW")
            print(f"  最大功率: {power_stats['max']:.2f} kW")
            print(f"  功率标准差: {power_stats['std']:.2f} kW")

            if len(clean_data) > 10:
                print(f"  功率与温差相关性: {correlation:.3f}")
                print(f"  功率与环境温度相关性: {correlation2:.3f}")

        plt.tight_layout()

        # 保存图片到对应文件夹
        save_path = os.path.join(self.folders[2], '热泵能耗与温差关系分析.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

        # 生成详细说明文件
        self._generate_energy_report()

    def analyze_influencing_factors(self):
        """分析影响室内温度的因素 - 专门针对指定的7个变量"""
        print("\n=== 影响室内温度因素分析 ===")

        # 指定的7个变量
        target_variables = [
            '室内温度均值',  # 目标变量
            '环境温度(℃)',
            '供温(℃)',
            '回温(℃)',
            '设定温度(℃)',
            '热泵功率(kw)',
            '流速(m3h)'
        ]

        # 为每个地点生成相关性热力图
        fig, axes = plt.subplots(1, 2, figsize=(16, 6))
        fig.suptitle('指定变量相关性热力图分析', fontsize=16, fontweight='bold')

        correlation_results = {}

        for i, (data, location) in enumerate([(self.data_location1, "地点1"), (self.data_location2, "地点2")]):
            # 检查哪些变量在数据中存在
            available_vars = [var for var in target_variables if var in data.columns]
            print(f"\n{location}可用变量: {available_vars}")

            if len(available_vars) < 2:
                print(f"{location}可用变量不足，跳过分析")
                continue

            # 提取相关变量的数据
            analysis_data = data[available_vars].dropna()

            if len(analysis_data) == 0:
                print(f"{location}清洗后无有效数据")
                continue

            # 计算相关性矩阵
            correlation_matrix = analysis_data.corr()
            correlation_results[location] = {
                'matrix': correlation_matrix,
                'variables': available_vars,
                'data_points': len(analysis_data)
            }

            # 绘制热力图
            sns.heatmap(correlation_matrix,
                       annot=True,
                       cmap='RdBu_r',
                       center=0,
                       square=True,
                       fmt='.3f',
                       cbar_kws={'shrink': 0.8},
                       ax=axes[i])

            axes[i].set_title(f'{location} - 变量相关性热力图\n(数据点: {len(analysis_data)})')
            axes[i].tick_params(axis='x', rotation=45)
            axes[i].tick_params(axis='y', rotation=0)

            # 打印关键相关性
            print(f"\n{location}关键相关性分析:")
            if '室内温度均值' in correlation_matrix.columns:
                indoor_corr = correlation_matrix['室内温度均值'].drop('室内温度均值').sort_values(key=abs, ascending=False)
                for var, corr in indoor_corr.items():
                    print(f"  {var} vs 室内温度均值: {corr:.3f}")

        plt.tight_layout()

        # 保存图片到对应文件夹
        save_path = os.path.join(self.folders[3], '指定变量相关性热力图.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

        # 生成详细说明文件
        self._generate_factors_report(correlation_results, target_variables)

    def _generate_fluctuation_report(self):
        """生成室内温度波动规律分析报告"""
        report_path = os.path.join(self.folders[0], '室内温度波动规律分析报告.txt')

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("室内温度波动规律分析报告\n")
            f.write("="*50 + "\n\n")

            f.write("一、分析目的\n")
            f.write("-"*20 + "\n")
            f.write("统计分析不同建筑的室内温度波动规律，包括：\n")
            f.write("1. 日内温度变化规律\n")
            f.write("2. 温度分布特征\n")
            f.write("3. 时间序列趋势\n")
            f.write("4. 舒适度评估\n\n")

            f.write("二、使用的方法\n")
            f.write("-"*20 + "\n")
            f.write("1. 描述性统计分析：计算均值、标准差、最值等统计量\n")
            f.write("2. 时间序列分析：按小时分组分析日内变化规律\n")
            f.write("3. 分布分析：绘制直方图分析温度分布特征\n")
            f.write("4. 舒适度分析：基于19-21℃标准评估达标率\n\n")

            f.write("三、数据处理\n")
            f.write("-"*20 + "\n")
            f.write("1. 数据清洗：移除异常温度值（<10℃或>35℃）\n")
            f.write("2. 时间特征提取：提取小时、日期、月份等时间特征\n")
            f.write("3. 采样处理：时间序列图采用每24小时采样一次\n\n")

            f.write("四、生成的图表说明\n")
            f.write("-"*20 + "\n")
            f.write("图表文件：室内温度波动规律分析.png\n\n")
            f.write("图表包含6个子图（2行3列）：\n")
            f.write("上排 - 地点1分析：\n")
            f.write("  左图：日内温度变化 - 显示24小时内温度变化规律，包含均值线和标准差阴影\n")
            f.write("  中图：温度分布 - 直方图显示温度分布特征，红线为均值，绿线为目标温度20℃\n")
            f.write("  右图：温度时间序列 - 显示整个时间段的温度变化趋势\n\n")
            f.write("下排 - 地点2分析：\n")
            f.write("  布局和内容与地点1相同\n\n")

            f.write("五、分析结果\n")
            f.write("-"*20 + "\n")

            # 分析每个地点的数据
            for data, location in [(self.data_location1, "地点1"), (self.data_location2, "地点2")]:
                if '室内温度均值' in data.columns:
                    temp_stats = data['室内温度均值'].describe()
                    comfort_range = (data['室内温度均值'] >= 19) & (data['室内温度均值'] <= 21)
                    comfort_rate = comfort_range.sum() / len(data) * 100

                    f.write(f"\n{location}分析结果：\n")
                    f.write(f"  数据点数：{len(data)}\n")
                    f.write(f"  平均温度：{temp_stats['mean']:.2f}℃\n")
                    f.write(f"  温度标准差：{temp_stats['std']:.2f}℃\n")
                    f.write(f"  温度范围：{temp_stats['min']:.2f}℃ - {temp_stats['max']:.2f}℃\n")
                    f.write(f"  温度波动幅度：{temp_stats['max'] - temp_stats['min']:.2f}℃\n")
                    f.write(f"  舒适度达标率：{comfort_rate:.1f}%\n")

                    # 日内变化特征
                    hourly_temp = data.groupby('小时')['室内温度均值'].mean()
                    max_hour = hourly_temp.idxmax()
                    min_hour = hourly_temp.idxmin()
                    f.write(f"  最高温度时段：{max_hour}点（{hourly_temp[max_hour]:.2f}℃）\n")
                    f.write(f"  最低温度时段：{min_hour}点（{hourly_temp[min_hour]:.2f}℃）\n")
                    f.write(f"  日内温差：{hourly_temp.max() - hourly_temp.min():.2f}℃\n")

            f.write("\n六、结论与建议\n")
            f.write("-"*20 + "\n")
            f.write("1. 温度控制稳定性：通过标准差和波动幅度评估系统控制稳定性\n")
            f.write("2. 舒适度表现：评估实际运行中的舒适度达标情况\n")
            f.write("3. 日内规律：识别温度变化的时间规律，为控制策略优化提供依据\n")
            f.write("4. 系统优化方向：\n")
            f.write("   - 提高温度控制精度，减少波动\n")
            f.write("   - 根据日内规律调整控制参数\n")
            f.write("   - 提高舒适度达标率\n")

        print(f"已生成报告：{report_path}")

    def generate_summary_report(self):
        """生成分析总结报告"""
        print("\n" + "="*60)
        print("                  分析总结报告")
        print("="*60)

        for i, (data, location) in enumerate([(self.data_location1, "地点1"), (self.data_location2, "地点2")]):
            print(f"\n【{location}分析结果】")
            print("-" * 30)

            if '室内温度均值' in data.columns:
                temp_stats = data['室内温度均值'].describe()
                print(f"1. 室内温度特征:")
                print(f"   - 平均温度: {temp_stats['mean']:.2f}℃")
                print(f"   - 温度波动范围: {temp_stats['max'] - temp_stats['min']:.2f}℃")
                print(f"   - 温度标准差: {temp_stats['std']:.2f}℃")

                # 舒适度分析
                comfort_range = (data['室内温度均值'] >= 19) & (data['室内温度均值'] <= 21)
                comfort_rate = comfort_range.sum() / len(data) * 100
                print(f"   - 舒适度达标率: {comfort_rate:.1f}%")

            if '室内温度均值' in data.columns and '环境温度(℃)' in data.columns:
                clean_data = data[['室内温度均值', '环境温度(℃)']].dropna()
                if len(clean_data) > 0:
                    correlation = clean_data['环境温度(℃)'].corr(clean_data['室内温度均值'])
                    print(f"\n2. 室内外温度相关性:")
                    print(f"   - 相关系数: {correlation:.3f}")

                    if abs(correlation) > 0.7:
                        strength = "强"
                    elif abs(correlation) > 0.3:
                        strength = "中等"
                    else:
                        strength = "弱"
                    print(f"   - 相关强度: {strength}相关")

            if '热泵功率(kw)' in data.columns:
                power_data = data['热泵功率(kw)'].dropna()
                power_data = power_data[power_data >= 0]
                if len(power_data) > 0:
                    print(f"\n3. 热泵能耗特征:")
                    print(f"   - 平均功率: {power_data.mean():.2f} kW")
                    print(f"   - 最大功率: {power_data.max():.2f} kW")
                    print(f"   - 功率变异系数: {power_data.std()/power_data.mean()*100:.1f}%")

        print("\n【建议与结论】")
        print("-" * 30)
        print("1. 建筑保温性能:")
        print("   - 分析室内温度波动幅度，评估建筑热惰性")
        print("   - 温度波动小的建筑具有更好的保温性能")

        print("\n2. 系统控制优化:")
        print("   - 根据室内外温度相关性调整控制策略")
        print("   - 强相关性表明需要更精确的环境温度补偿")

        print("\n3. 能耗优化方向:")
        print("   - 分析功率与温差关系，优化供回水温度设定")
        print("   - 利用建筑热惰性，在低电价时段预热")

    def run_analysis(self):
        """运行完整分析流程"""
        print("开始空气源热泵数据分析...")

        # 1. 加载数据
        if not self.load_data():
            return

        # 2. 数据预处理
        self.preprocess_data()

        # 3. 室内温度波动规律分析
        self.analyze_temperature_fluctuation()

        # 4. 室内外温度相关性分析
        self.analyze_indoor_outdoor_correlation()

        # 5. 热泵能耗与温差关系分析
        self.analyze_energy_temperature_relationship()

        # 6. 影响因素分析
        self.analyze_influencing_factors()

        # 7. 生成总结报告
        self.generate_summary_report()

        print("\n分析完成！所有图表已保存。")

    def _generate_correlation_report(self):
        """生成室内外温度相关性分析报告"""
        report_path = os.path.join(self.folders[1], '室内外温度相关性分析报告.txt')

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("室内外温度相关性分析报告\n")
            f.write("="*50 + "\n\n")

            f.write("一、分析目的\n")
            f.write("-"*20 + "\n")
            f.write("分析室内外温度的相关性，评估建筑保温性能和系统控制效果：\n")
            f.write("1. 计算室内外温度相关系数\n")
            f.write("2. 绘制散点图和回归线\n")
            f.write("3. 分析室内外温差变化\n")
            f.write("4. 评估建筑热性能\n\n")

            f.write("二、使用的方法\n")
            f.write("-"*20 + "\n")
            f.write("1. 皮尔逊相关系数：测量线性相关强度\n")
            f.write("2. 线性回归分析：拟合回归线，建立预测关系\n")
            f.write("3. 散点图可视化：直观显示两变量关系\n")
            f.write("4. 时间序列分析：分析温差随时间的变化\n\n")

            f.write("三、数据处理\n")
            f.write("-"*20 + "\n")
            f.write("1. 数据清洗：移除缺失值和异常值\n")
            f.write("2. 温差计算：室内温度 - 环境温度\n")
            f.write("3. 时间采样：温差时间序列采用每24小时采样一次\n\n")

            f.write("四、生成的图表说明\n")
            f.write("-"*20 + "\n")
            f.write("图表文件：室内外温度相关性分析.png\n\n")
            f.write("图表包含4个子图（2行2列）：\n")
            f.write("上排 - 地点1分析：\n")
            f.write("  左图：相关性散点图 - 环境温度vs室内温度，包含回归线和相关系数\n")
            f.write("  右图：温差时间序列 - 显示室内外温差随时间的变化\n")
            f.write("下排 - 地点2分析：\n")
            f.write("  布局和内容与地点1相同\n\n")

            f.write("五、分析结果\n")
            f.write("-"*20 + "\n")

            # 分析每个地点的相关性
            for data, location in [(self.data_location1, "地点1"), (self.data_location2, "地点2")]:
                if '室内温度均值' in data.columns and '环境温度(℃)' in data.columns:
                    clean_data = data[['室内温度均值', '环境温度(℃)']].dropna()
                    if len(clean_data) > 0:
                        correlation = clean_data['环境温度(℃)'].corr(clean_data['室内温度均值'])

                        f.write(f"\n{location}相关性分析：\n")
                        f.write(f"  有效数据点：{len(clean_data)}\n")
                        f.write(f"  相关系数：{correlation:.3f}\n")

                        if abs(correlation) > 0.7:
                            strength = "强相关"
                        elif abs(correlation) > 0.3:
                            strength = "中等相关"
                        else:
                            strength = "弱相关"
                        f.write(f"  相关强度：{strength}\n")

                        # 温差统计
                        if '室内外温差' in data.columns:
                            temp_diff = data['室内外温差'].dropna()
                            f.write(f"  平均温差：{temp_diff.mean():.2f}℃\n")
                            f.write(f"  温差标准差：{temp_diff.std():.2f}℃\n")
                            f.write(f"  温差范围：{temp_diff.min():.2f}℃ - {temp_diff.max():.2f}℃\n")

            f.write("\n六、结论与建议\n")
            f.write("-"*20 + "\n")
            f.write("1. 建筑保温性能评估：\n")
            f.write("   - 相关系数越小，建筑保温性能越好\n")
            f.write("   - 弱相关表明室内温度受外界影响小\n")
            f.write("2. 系统控制效果：\n")
            f.write("   - 低相关性说明控制系统能有效隔离外界干扰\n")
            f.write("   - 温差稳定性反映控制精度\n")
            f.write("3. 优化建议：\n")
            f.write("   - 对于高相关性建筑，加强环境温度补偿\n")
            f.write("   - 利用建筑热惰性进行预测控制\n")

        print(f"已生成报告：{report_path}")

    def _generate_energy_report(self):
        """生成热泵能耗与温差关系分析报告"""
        report_path = os.path.join(self.folders[2], '热泵能耗与温差关系分析报告.txt')

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("热泵能耗与温差关系分析报告\n")
            f.write("="*50 + "\n\n")

            f.write("一、分析目的\n")
            f.write("-"*20 + "\n")
            f.write("分析热泵功率与温差的定量关系，建立能耗预测模型：\n")
            f.write("1. 功率与室内外温差的关系\n")
            f.write("2. 功率与环境温度的关系\n")
            f.write("3. 能耗特征统计分析\n")
            f.write("4. 建立定量关系模型\n\n")

            f.write("二、使用的方法\n")
            f.write("-"*20 + "\n")
            f.write("1. 相关性分析：计算功率与温度变量的相关系数\n")
            f.write("2. 线性回归：建立功率预测模型\n")
            f.write("3. 散点图分析：可视化变量间关系\n")
            f.write("4. 描述性统计：分析功率分布特征\n\n")

            f.write("三、数据处理\n")
            f.write("-"*20 + "\n")
            f.write("1. 数据清洗：移除负功率值和缺失值\n")
            f.write("2. 异常值处理：保留合理范围内的功率数据\n")
            f.write("3. 变量计算：计算室内外温差\n\n")

            f.write("四、生成的图表说明\n")
            f.write("-"*20 + "\n")
            f.write("图表文件：热泵能耗与温差关系分析.png\n\n")
            f.write("图表包含4个子图（2行2列）：\n")
            f.write("上排 - 地点1分析：\n")
            f.write("  左图：功率vs温差 - 散点图显示功率与室内外温差关系，包含回归线\n")
            f.write("  右图：功率vs环境温度 - 散点图显示功率与环境温度关系\n")
            f.write("下排 - 地点2分析：\n")
            f.write("  布局和内容与地点1相同\n\n")

            f.write("五、分析结果\n")
            f.write("-"*20 + "\n")

            # 分析每个地点的能耗数据
            for data, location in [(self.data_location1, "地点1"), (self.data_location2, "地点2")]:
                if '热泵功率(kw)' in data.columns:
                    clean_data = data[['热泵功率(kw)', '室内外温差', '环境温度(℃)']].dropna()
                    clean_data = clean_data[clean_data['热泵功率(kw)'] >= 0]

                    if len(clean_data) > 0:
                        power_stats = clean_data['热泵功率(kw)'].describe()

                        f.write(f"\n{location}能耗分析：\n")
                        f.write(f"  有效数据点：{len(clean_data)}\n")
                        f.write(f"  平均功率：{power_stats['mean']:.2f} kW\n")
                        f.write(f"  功率标准差：{power_stats['std']:.2f} kW\n")
                        f.write(f"  功率范围：{power_stats['min']:.2f} - {power_stats['max']:.2f} kW\n")
                        f.write(f"  功率变异系数：{power_stats['std']/power_stats['mean']*100:.1f}%\n")

                        # 相关性分析
                        if len(clean_data) > 10:
                            corr_temp_diff = clean_data['室内外温差'].corr(clean_data['热泵功率(kw)'])
                            corr_env_temp = clean_data['环境温度(℃)'].corr(clean_data['热泵功率(kw)'])

                            f.write(f"  功率与温差相关性：{corr_temp_diff:.3f}\n")
                            f.write(f"  功率与环境温度相关性：{corr_env_temp:.3f}\n")

            f.write("\n六、结论与建议\n")
            f.write("-"*20 + "\n")
            f.write("1. 能耗特征分析：\n")
            f.write("   - 平均功率反映系统负荷水平\n")
            f.write("   - 功率变异系数反映负荷波动程度\n")
            f.write("2. 定量关系建立：\n")
            f.write("   - 相关性强的变量可用于功率预测\n")
            f.write("   - 建立基于温差的能耗预测模型\n")
            f.write("3. 优化建议：\n")
            f.write("   - 根据温差关系优化功率控制策略\n")
            f.write("   - 利用环境温度进行负荷预测\n")
            f.write("   - 实现基于预测的节能控制\n")

        print(f"已生成报告：{report_path}")

    def _generate_factors_report(self, correlation_results, target_variables):
        """生成影响因素分析报告"""
        report_path = os.path.join(self.folders[3], '影响因素分析报告.txt')

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("影响室内温度的因素分析报告\n")
            f.write("="*50 + "\n\n")

            f.write("一、分析目的\n")
            f.write("-"*20 + "\n")
            f.write("专门分析指定的7个变量对室内温度的影响程度：\n")
            f.write("1. 计算各变量间的相关性矩阵\n")
            f.write("2. 识别对室内温度影响最大的因素\n")
            f.write("3. 分析变量间的相互关系\n")
            f.write("4. 为预测模型提供特征选择依据\n\n")

            f.write("二、分析的变量\n")
            f.write("-"*20 + "\n")
            f.write("目标变量：室内温度均值（℃）\n")
            f.write("影响因素：\n")
            for i, var in enumerate(target_variables[1:], 1):
                f.write(f"  {i}. {var}\n")
            f.write("\n")

            f.write("三、使用的方法\n")
            f.write("-"*20 + "\n")
            f.write("1. 皮尔逊相关系数：测量线性相关强度\n")
            f.write("2. 相关性矩阵：全面分析变量间关系\n")
            f.write("3. 热力图可视化：直观显示相关性强度\n")
            f.write("4. 排序分析：按影响程度排序\n\n")

            f.write("四、数据处理\n")
            f.write("-"*20 + "\n")
            f.write("1. 变量筛选：仅保留指定的7个变量\n")
            f.write("2. 数据清洗：移除缺失值\n")
            f.write("3. 相关性计算：计算所有变量间的相关系数\n\n")

            f.write("五、生成的图表说明\n")
            f.write("-"*20 + "\n")
            f.write("图表文件：指定变量相关性热力图.png\n\n")
            f.write("图表包含2个热力图（1行2列）：\n")
            f.write("左图：地点1相关性热力图\n")
            f.write("  - 颜色深度表示相关性强度\n")
            f.write("  - 红色表示正相关，蓝色表示负相关\n")
            f.write("  - 数值标注显示精确的相关系数\n")
            f.write("右图：地点2相关性热力图\n")
            f.write("  - 布局和含义与地点1相同\n\n")

            f.write("六、分析结果\n")
            f.write("-"*20 + "\n")

            # 详细分析结果
            for location, results in correlation_results.items():
                f.write(f"\n{location}分析结果：\n")
                f.write(f"  可用变量：{len(results['variables'])}个\n")
                f.write(f"  变量列表：{', '.join(results['variables'])}\n")
                f.write(f"  有效数据点：{results['data_points']}\n\n")

                # 相关性矩阵
                correlation_matrix = results['matrix']
                f.write(f"  相关性矩阵：\n")
                for i, var1 in enumerate(results['variables']):
                    f.write(f"    {var1}:\n")
                    for j, var2 in enumerate(results['variables']):
                        if i != j:
                            corr_val = correlation_matrix.loc[var1, var2]
                            f.write(f"      与{var2}: {corr_val:.3f}\n")

                # 对室内温度的影响排序
                if '室内温度均值' in correlation_matrix.columns:
                    indoor_corr = correlation_matrix['室内温度均值'].drop('室内温度均值')
                    sorted_corr = indoor_corr.sort_values(key=abs, ascending=False)

                    f.write(f"\n  对室内温度影响排序（按相关性绝对值）：\n")
                    for rank, (var, corr) in enumerate(sorted_corr.items(), 1):
                        strength = "强" if abs(corr) > 0.7 else "中等" if abs(corr) > 0.3 else "弱"
                        direction = "正" if corr > 0 else "负"
                        f.write(f"    {rank}. {var}: {corr:.3f} ({strength}{direction}相关)\n")

                # 强相关变量对识别
                f.write(f"\n  强相关变量对（|r| > 0.7）：\n")
                strong_pairs = []
                for i, var1 in enumerate(results['variables']):
                    for j, var2 in enumerate(results['variables']):
                        if i < j:  # 避免重复
                            corr_val = correlation_matrix.loc[var1, var2]
                            if abs(corr_val) > 0.7:
                                strong_pairs.append((var1, var2, corr_val))

                if strong_pairs:
                    for var1, var2, corr in strong_pairs:
                        f.write(f"    {var1} ↔ {var2}: {corr:.3f}\n")
                else:
                    f.write("    无强相关变量对\n")

            f.write("\n七、结论与建议\n")
            f.write("-"*20 + "\n")
            f.write("1. 主要影响因素识别：\n")
            f.write("   - 相关系数绝对值大的变量是主要影响因素\n")
            f.write("   - 正相关表示同向变化，负相关表示反向变化\n")
            f.write("2. 变量间关系分析：\n")
            f.write("   - 强相关变量对可能存在多重共线性\n")
            f.write("   - 建模时需要考虑变量选择和降维\n")
            f.write("3. 预测模型建议：\n")
            f.write("   - 优先选择与室内温度相关性强的变量\n")
            f.write("   - 避免同时使用强相关的变量\n")
            f.write("   - 考虑使用主成分分析等降维方法\n")
            f.write("4. 控制策略建议：\n")
            f.write("   - 重点控制影响最大的变量\n")
            f.write("   - 建立多变量协同控制策略\n")
            f.write("   - 根据相关性强度调整控制权重\n")

        print(f"已生成报告：{report_path}")

if __name__ == "__main__":
    analyzer = HeatPumpAnalyzer()
    analyzer.run_analysis()
