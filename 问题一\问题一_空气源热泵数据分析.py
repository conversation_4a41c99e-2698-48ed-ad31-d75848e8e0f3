#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
空气源热泵数据分析 - 问题一
分析室内温度波动规律、室内外温度相关性、热泵能耗与温差关系以及影响因素
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class HeatPumpAnalyzer:
    def __init__(self):
        self.data_location1 = None
        self.data_location2 = None

    def load_data(self):
        """加载数据"""
        print("正在加载数据...")
        try:
            # 读取地点1数据
            self.data_location1 = pd.read_csv('../data/附件2/地点1/地点1_完整合并表.csv', encoding='utf-8')
            print(f"地点1数据加载成功，共{len(self.data_location1)}行")

            # 读取地点2数据
            self.data_location2 = pd.read_csv('../data/附件2/地点2/地点2_完整合并表.csv', encoding='utf-8')
            print(f"地点2数据加载成功，共{len(self.data_location2)}行")

        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
        return True

    def preprocess_data(self):
        """数据预处理"""
        print("正在进行数据预处理...")

        for i, (data, location) in enumerate([(self.data_location1, "地点1"), (self.data_location2, "地点2")]):
            print(f"\n处理{location}数据:")

            # 转换时间列
            data['时间'] = pd.to_datetime(data['时间'])

            # 提取关键列 - 使用正确的列名
            key_columns = ['时间', '环境温度(℃)', '室内温度均值', '热泵功率(kw)', '设定温度(℃)']

            # 检查列是否存在
            existing_columns = [col for col in key_columns if col in data.columns]
            print(f"可用列: {existing_columns}")

            # 数据清洗
            # 处理异常值和缺失值
            if '室内温度均值' in data.columns:
                # 移除明显异常的温度值
                data = data[(data['室内温度均值'] >= 10) & (data['室内温度均值'] <= 35)]

            if '环境温度(℃)' in data.columns:
                data = data[(data['环境温度(℃)'] >= -20) & (data['环境温度(℃)'] <= 40)]

            # 添加时间特征
            data['小时'] = data['时间'].dt.hour
            data['日期'] = data['时间'].dt.date
            data['月份'] = data['时间'].dt.month
            data['星期'] = data['时间'].dt.dayofweek

            # 计算温差
            if '室内温度均值' in data.columns and '环境温度(℃)' in data.columns:
                data['室内外温差'] = data['室内温度均值'] - data['环境温度(℃)']

            # 更新数据
            if i == 0:
                self.data_location1 = data
            else:
                self.data_location2 = data

            print(f"{location}预处理完成，剩余{len(data)}行数据")

    def analyze_temperature_fluctuation(self):
        """分析室内温度波动规律"""
        print("\n=== 室内温度波动规律分析 ===")

        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('室内温度波动规律分析', fontsize=16, fontweight='bold')

        for i, (data, location) in enumerate([(self.data_location1, "地点1"), (self.data_location2, "地点2")]):
            if '室内温度均值' not in data.columns:
                continue

            # 1. 日内温度变化规律
            hourly_temp = data.groupby('小时')['室内温度均值'].agg(['mean', 'std', 'min', 'max'])

            axes[i, 0].plot(hourly_temp.index, hourly_temp['mean'], 'o-', linewidth=2, markersize=4)
            axes[i, 0].fill_between(hourly_temp.index,
                                   hourly_temp['mean'] - hourly_temp['std'],
                                   hourly_temp['mean'] + hourly_temp['std'],
                                   alpha=0.3)
            axes[i, 0].set_title(f'{location} - 日内温度变化')
            axes[i, 0].set_xlabel('小时')
            axes[i, 0].set_ylabel('室内温度 (℃)')
            axes[i, 0].grid(True, alpha=0.3)

            # 2. 温度分布直方图
            axes[i, 1].hist(data['室内温度均值'].dropna(), bins=30, alpha=0.7, density=True)
            axes[i, 1].axvline(data['室内温度均值'].mean(), color='red', linestyle='--',
                              label=f'均值: {data["室内温度均值"].mean():.1f}℃')
            axes[i, 1].axvline(20, color='green', linestyle='--', alpha=0.7, label='目标温度: 20℃')
            axes[i, 1].set_title(f'{location} - 温度分布')
            axes[i, 1].set_xlabel('室内温度 (℃)')
            axes[i, 1].set_ylabel('密度')
            axes[i, 1].legend()
            axes[i, 1].grid(True, alpha=0.3)

            # 3. 时间序列趋势
            sample_data = data.iloc[::24]  # 每天采样一次
            axes[i, 2].plot(sample_data['时间'], sample_data['室内温度均值'], alpha=0.7)
            axes[i, 2].set_title(f'{location} - 温度时间序列')
            axes[i, 2].set_xlabel('时间')
            axes[i, 2].set_ylabel('室内温度 (℃)')
            axes[i, 2].tick_params(axis='x', rotation=45)
            axes[i, 2].grid(True, alpha=0.3)

            # 统计信息
            temp_stats = data['室内温度均值'].describe()
            print(f"\n{location}室内温度统计:")
            print(f"  均值: {temp_stats['mean']:.2f}℃")
            print(f"  标准差: {temp_stats['std']:.2f}℃")
            print(f"  最小值: {temp_stats['min']:.2f}℃")
            print(f"  最大值: {temp_stats['max']:.2f}℃")
            print(f"  温度范围: {temp_stats['max'] - temp_stats['min']:.2f}℃")

            # 舒适度分析
            comfort_range = (data['室内温度均值'] >= 19) & (data['室内温度均值'] <= 21)
            comfort_rate = comfort_range.sum() / len(data) * 100
            print(f"  舒适度(19-21℃)达标率: {comfort_rate:.1f}%")

        plt.tight_layout()
        plt.savefig('室内温度波动规律分析.png', dpi=300, bbox_inches='tight')
        plt.show()

    def analyze_indoor_outdoor_correlation(self):
        """分析室内外温度相关性"""
        print("\n=== 室内外温度相关性分析 ===")

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('室内外温度相关性分析', fontsize=16, fontweight='bold')

        for i, (data, location) in enumerate([(self.data_location1, "地点1"), (self.data_location2, "地点2")]):
            if '室内温度均值' not in data.columns or '环境温度(℃)' not in data.columns:
                continue

            # 清理数据
            clean_data = data[['室内温度均值', '环境温度(℃)']].dropna()

            if len(clean_data) == 0:
                continue

            # 1. 散点图和回归线
            axes[i, 0].scatter(clean_data['环境温度(℃)'], clean_data['室内温度均值'],
                              alpha=0.5, s=10)

            # 计算回归线
            z = np.polyfit(clean_data['环境温度(℃)'], clean_data['室内温度均值'], 1)
            p = np.poly1d(z)
            x_range = np.linspace(clean_data['环境温度(℃)'].min(), clean_data['环境温度(℃)'].max(), 100)
            axes[i, 0].plot(x_range, p(x_range), "r--", alpha=0.8, linewidth=2)

            # 计算相关系数
            correlation = clean_data['环境温度(℃)'].corr(clean_data['室内温度均值'])

            axes[i, 0].set_title(f'{location} - 相关性 (r={correlation:.3f})')
            axes[i, 0].set_xlabel('环境温度 (℃)')
            axes[i, 0].set_ylabel('室内温度 (℃)')
            axes[i, 0].grid(True, alpha=0.3)

            # 2. 温差时间序列
            if '室内外温差' in data.columns:
                sample_data = data.iloc[::24]  # 每天采样一次
                axes[i, 1].plot(sample_data['时间'], sample_data['室内外温差'], alpha=0.7)
                axes[i, 1].axhline(y=0, color='red', linestyle='--', alpha=0.5)
                axes[i, 1].set_title(f'{location} - 室内外温差变化')
                axes[i, 1].set_xlabel('时间')
                axes[i, 1].set_ylabel('温差 (℃)')
                axes[i, 1].tick_params(axis='x', rotation=45)
                axes[i, 1].grid(True, alpha=0.3)

            print(f"\n{location}相关性分析:")
            print(f"  相关系数: {correlation:.3f}")

            if abs(correlation) > 0.7:
                print("  相关性: 强相关")
            elif abs(correlation) > 0.3:
                print("  相关性: 中等相关")
            else:
                print("  相关性: 弱相关")

        plt.tight_layout()
        plt.savefig('室内外温度相关性分析.png', dpi=300, bbox_inches='tight')
        plt.show()

    def analyze_energy_temperature_relationship(self):
        """分析热泵能耗与温差的定量关系"""
        print("\n=== 热泵能耗与温差关系分析 ===")

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('热泵能耗与温差关系分析', fontsize=16, fontweight='bold')

        for i, (data, location) in enumerate([(self.data_location1, "地点1"), (self.data_location2, "地点2")]):
            if '热泵功率(kw)' not in data.columns or '室内外温差' not in data.columns:
                continue

            # 清理数据
            clean_data = data[['热泵功率(kw)', '室内外温差', '环境温度(℃)']].dropna()
            clean_data = clean_data[clean_data['热泵功率(kw)'] >= 0]  # 移除负功率值

            if len(clean_data) == 0:
                continue

            # 1. 功率与温差散点图
            axes[i, 0].scatter(clean_data['室内外温差'], clean_data['热泵功率(kw)'],
                              alpha=0.6, s=15)

            # 拟合回归线
            if len(clean_data) > 10:
                z = np.polyfit(clean_data['室内外温差'], clean_data['热泵功率(kw)'], 1)
                p = np.poly1d(z)
                x_range = np.linspace(clean_data['室内外温差'].min(),
                                    clean_data['室内外温差'].max(), 100)
                axes[i, 0].plot(x_range, p(x_range), "r--", alpha=0.8, linewidth=2)

                # 计算相关系数
                correlation = clean_data['室内外温差'].corr(clean_data['热泵功率(kw)'])
                axes[i, 0].set_title(f'{location} - 功率vs温差 (r={correlation:.3f})')
            else:
                axes[i, 0].set_title(f'{location} - 功率vs温差')

            axes[i, 0].set_xlabel('室内外温差 (℃)')
            axes[i, 0].set_ylabel('热泵功率 (kW)')
            axes[i, 0].grid(True, alpha=0.3)

            # 2. 功率与环境温度关系
            axes[i, 1].scatter(clean_data['环境温度(℃)'], clean_data['热泵功率(kw)'],
                              alpha=0.6, s=15, color='orange')

            if len(clean_data) > 10:
                z2 = np.polyfit(clean_data['环境温度(℃)'], clean_data['热泵功率(kw)'], 1)
                p2 = np.poly1d(z2)
                x_range2 = np.linspace(clean_data['环境温度(℃)'].min(),
                                     clean_data['环境温度(℃)'].max(), 100)
                axes[i, 1].plot(x_range2, p2(x_range2), "r--", alpha=0.8, linewidth=2)

                correlation2 = clean_data['环境温度(℃)'].corr(clean_data['热泵功率(kw)'])
                axes[i, 1].set_title(f'{location} - 功率vs环境温度 (r={correlation2:.3f})')
            else:
                axes[i, 1].set_title(f'{location} - 功率vs环境温度')

            axes[i, 1].set_xlabel('环境温度 (℃)')
            axes[i, 1].set_ylabel('热泵功率 (kW)')
            axes[i, 1].grid(True, alpha=0.3)

            # 统计分析
            power_stats = clean_data['热泵功率(kw)'].describe()
            print(f"\n{location}热泵功率统计:")
            print(f"  平均功率: {power_stats['mean']:.2f} kW")
            print(f"  最大功率: {power_stats['max']:.2f} kW")
            print(f"  功率标准差: {power_stats['std']:.2f} kW")

            if len(clean_data) > 10:
                print(f"  功率与温差相关性: {correlation:.3f}")
                print(f"  功率与环境温度相关性: {correlation2:.3f}")

        plt.tight_layout()
        plt.savefig('热泵能耗与温差关系分析.png', dpi=300, bbox_inches='tight')
        plt.show()

    def analyze_influencing_factors(self):
        """深度分析影响室内温度的因素"""
        print("\n=== 影响室内温度因素深度分析 ===")

        # 1. 基础影响因素分析
        self._basic_factor_analysis()

        # 2. 相关性矩阵分析
        self._correlation_matrix_analysis()

        # 3. 多元回归分析
        self._multiple_regression_analysis()

        # 4. 特征重要性分析
        self._feature_importance_analysis()

        # 5. 时间序列分解分析
        self._time_series_decomposition()

    def _basic_factor_analysis(self):
        """基础影响因素分析"""
        fig, axes = plt.subplots(3, 4, figsize=(20, 15))
        fig.suptitle('影响室内温度的基础因素分析', fontsize=16, fontweight='bold')

        for i, (data, location) in enumerate([(self.data_location1, "地点1"), (self.data_location2, "地点2")]):
            if '室内温度均值' not in data.columns:
                continue

            # 1. 时间因素 - 小时影响
            hourly_stats = data.groupby('小时')['室内温度均值'].agg(['mean', 'std'])
            axes[i, 0].errorbar(hourly_stats.index, hourly_stats['mean'],
                               yerr=hourly_stats['std'], fmt='o-', capsize=3)
            axes[i, 0].set_title(f'{location} - 小时影响（含标准差）')
            axes[i, 0].set_xlabel('小时')
            axes[i, 0].set_ylabel('室内温度 (℃)')
            axes[i, 0].grid(True, alpha=0.3)

            # 2. 星期影响
            weekly_temp = data.groupby('星期')['室内温度均值'].mean()
            week_names = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
            axes[i, 1].bar(range(len(weekly_temp)), weekly_temp.values, alpha=0.7)
            axes[i, 1].set_xticks(range(len(weekly_temp)))
            axes[i, 1].set_xticklabels(week_names, rotation=45)
            axes[i, 1].set_title(f'{location} - 星期影响')
            axes[i, 1].set_ylabel('平均室内温度 (℃)')
            axes[i, 1].grid(True, alpha=0.3)

            # 3. 月份影响
            monthly_temp = data.groupby('月份')['室内温度均值'].mean()
            axes[i, 2].plot(monthly_temp.index, monthly_temp.values, 'o-', linewidth=2, markersize=6)
            axes[i, 2].set_title(f'{location} - 月份影响')
            axes[i, 2].set_xlabel('月份')
            axes[i, 2].set_ylabel('平均室内温度 (℃)')
            axes[i, 2].grid(True, alpha=0.3)

            # 4. 热泵功率影响
            if '热泵功率(kw)' in data.columns:
                clean_data = data[['室内温度均值', '热泵功率(kw)']].dropna()
                clean_data = clean_data[clean_data['热泵功率(kw)'] >= 0]
                if len(clean_data) > 100:
                    # 按功率分组
                    power_bins = pd.qcut(clean_data['热泵功率(kw)'], q=10, duplicates='drop')
                    power_grouped = clean_data.groupby(power_bins)['室内温度均值'].mean()

                    bin_centers = [interval.mid for interval in power_grouped.index]
                    axes[i, 3].plot(bin_centers, power_grouped.values, 'o-', linewidth=2)
                    axes[i, 3].set_title(f'{location} - 热泵功率影响')
                    axes[i, 3].set_xlabel('热泵功率 (kW)')
                    axes[i, 3].set_ylabel('平均室内温度 (℃)')
                    axes[i, 3].grid(True, alpha=0.3)
                else:
                    axes[i, 3].text(0.5, 0.5, '数据不足', ha='center', va='center', transform=axes[i, 3].transAxes)
                    axes[i, 3].set_title(f'{location} - 热泵功率影响 (数据不足)')
            else:
                axes[i, 3].text(0.5, 0.5, '无功率数据', ha='center', va='center', transform=axes[i, 3].transAxes)
                axes[i, 3].set_title(f'{location} - 热泵功率影响 (无数据)')

        # 隐藏多余的子图
        for j in range(2, 3):
            for k in range(4):
                axes[j, k].set_visible(False)

        plt.tight_layout()
        plt.savefig('基础影响因素分析.png', dpi=300, bbox_inches='tight')
        plt.show()

    def _correlation_matrix_analysis(self):
        """相关性矩阵分析"""
        print("\n--- 相关性矩阵分析 ---")

        fig, axes = plt.subplots(1, 2, figsize=(16, 6))
        fig.suptitle('影响因素相关性矩阵分析', fontsize=16, fontweight='bold')

        for i, (data, location) in enumerate([(self.data_location1, "地点1"), (self.data_location2, "地点2")]):
            # 选择数值型列进行相关性分析
            numeric_cols = ['室内温度均值', '环境温度(℃)', '热泵功率(kw)', '设定温度(℃)',
                           '小时', '星期', '月份', '室内外温差']

            # 筛选存在的列
            available_cols = [col for col in numeric_cols if col in data.columns]

            if len(available_cols) > 2:
                corr_data = data[available_cols].dropna()

                if len(corr_data) > 0:
                    # 计算相关性矩阵
                    corr_matrix = corr_data.corr()

                    # 绘制热力图
                    im = axes[i].imshow(corr_matrix.values, cmap='RdBu_r', vmin=-1, vmax=1)

                    # 设置标签
                    axes[i].set_xticks(range(len(corr_matrix.columns)))
                    axes[i].set_yticks(range(len(corr_matrix.columns)))
                    axes[i].set_xticklabels(corr_matrix.columns, rotation=45, ha='right')
                    axes[i].set_yticklabels(corr_matrix.columns)

                    # 添加数值标注
                    for row in range(len(corr_matrix.columns)):
                        for col in range(len(corr_matrix.columns)):
                            value = corr_matrix.iloc[row, col]
                            color = 'white' if abs(value) > 0.5 else 'black'
                            axes[i].text(col, row, f'{value:.2f}',
                                       ha='center', va='center', color=color, fontsize=8)

                    axes[i].set_title(f'{location} - 相关性矩阵')

                    # 打印重要相关性
                    print(f"\n{location}重要相关性（|r| > 0.3）:")
                    for col1 in corr_matrix.columns:
                        for col2 in corr_matrix.columns:
                            if col1 != col2:
                                corr_val = corr_matrix.loc[col1, col2]
                                if abs(corr_val) > 0.3:
                                    print(f"  {col1} vs {col2}: {corr_val:.3f}")
                else:
                    axes[i].text(0.5, 0.5, '数据不足', ha='center', va='center', transform=axes[i].transAxes)
                    axes[i].set_title(f'{location} - 相关性矩阵 (数据不足)')
            else:
                axes[i].text(0.5, 0.5, '可用列不足', ha='center', va='center', transform=axes[i].transAxes)
                axes[i].set_title(f'{location} - 相关性矩阵 (列不足)')

        # 添加颜色条
        plt.colorbar(im, ax=axes, shrink=0.8, label='相关系数')
        plt.tight_layout()
        plt.savefig('相关性矩阵分析.png', dpi=300, bbox_inches='tight')
        plt.show()

    def _multiple_regression_analysis(self):
        """多元回归分析"""
        print("\n--- 多元回归分析 ---")

        from sklearn.linear_model import LinearRegression
        from sklearn.metrics import r2_score, mean_squared_error
        from sklearn.preprocessing import StandardScaler

        for i, (data, location) in enumerate([(self.data_location1, "地点1"), (self.data_location2, "地点2")]):
            print(f"\n{location}多元回归分析:")

            # 准备特征变量
            feature_cols = ['环境温度(℃)', '热泵功率(kw)', '设定温度(℃)', '小时', '星期', '月份']
            available_features = [col for col in feature_cols if col in data.columns]

            if len(available_features) >= 2 and '室内温度均值' in data.columns:
                # 准备数据
                regression_data = data[available_features + ['室内温度均值']].dropna()

                if len(regression_data) > 50:
                    X = regression_data[available_features]
                    y = regression_data['室内温度均值']

                    # 标准化特征
                    scaler = StandardScaler()
                    X_scaled = scaler.fit_transform(X)

                    # 拟合回归模型
                    model = LinearRegression()
                    model.fit(X_scaled, y)

                    # 预测和评估
                    y_pred = model.predict(X_scaled)
                    r2 = r2_score(y, y_pred)
                    rmse = np.sqrt(mean_squared_error(y, y_pred))

                    print(f"  R² 得分: {r2:.4f}")
                    print(f"  RMSE: {rmse:.4f}℃")

                    # 特征系数分析
                    print(f"  特征重要性（标准化系数）:")
                    for feature, coef in zip(available_features, model.coef_):
                        print(f"    {feature}: {coef:.4f}")

                    print(f"  截距: {model.intercept_:.4f}")

                    # 计算特征贡献度
                    feature_importance = np.abs(model.coef_)
                    feature_importance_norm = feature_importance / np.sum(feature_importance) * 100

                    print(f"  特征贡献度（%）:")
                    for feature, importance in zip(available_features, feature_importance_norm):
                        print(f"    {feature}: {importance:.2f}%")
                else:
                    print(f"  数据量不足（{len(regression_data)}行），无法进行回归分析")
            else:
                print(f"  可用特征不足，无法进行回归分析")

    def _feature_importance_analysis(self):
        """特征重要性分析（使用随机森林）"""
        print("\n--- 特征重要性分析（随机森林） ---")

        try:
            from sklearn.ensemble import RandomForestRegressor
            from sklearn.model_selection import train_test_split
            from sklearn.metrics import r2_score, mean_squared_error

            fig, axes = plt.subplots(1, 2, figsize=(16, 6))
            fig.suptitle('特征重要性分析（随机森林）', fontsize=16, fontweight='bold')

            for i, (data, location) in enumerate([(self.data_location1, "地点1"), (self.data_location2, "地点2")]):
                print(f"\n{location}特征重要性分析:")

                # 准备特征变量
                feature_cols = ['环境温度(℃)', '热泵功率(kw)', '设定温度(℃)', '小时', '星期', '月份', '室内外温差']
                available_features = [col for col in feature_cols if col in data.columns]

                if len(available_features) >= 3 and '室内温度均值' in data.columns:
                    # 准备数据
                    rf_data = data[available_features + ['室内温度均值']].dropna()

                    if len(rf_data) > 100:
                        X = rf_data[available_features]
                        y = rf_data['室内温度均值']

                        # 分割数据
                        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

                        # 训练随机森林
                        rf_model = RandomForestRegressor(n_estimators=100, random_state=42, max_depth=10)
                        rf_model.fit(X_train, y_train)

                        # 预测和评估
                        y_pred = rf_model.predict(X_test)
                        r2 = r2_score(y_test, y_pred)
                        rmse = np.sqrt(mean_squared_error(y_test, y_pred))

                        print(f"  随机森林 R² 得分: {r2:.4f}")
                        print(f"  随机森林 RMSE: {rmse:.4f}℃")

                        # 特征重要性
                        feature_importance = rf_model.feature_importances_
                        feature_importance_norm = feature_importance / np.sum(feature_importance) * 100

                        print(f"  特征重要性排序:")
                        importance_df = pd.DataFrame({
                            'feature': available_features,
                            'importance': feature_importance_norm
                        }).sort_values('importance', ascending=False)

                        for _, row in importance_df.iterrows():
                            print(f"    {row['feature']}: {row['importance']:.2f}%")

                        # 绘制特征重要性图
                        axes[i].barh(range(len(importance_df)), importance_df['importance'])
                        axes[i].set_yticks(range(len(importance_df)))
                        axes[i].set_yticklabels(importance_df['feature'])
                        axes[i].set_xlabel('重要性 (%)')
                        axes[i].set_title(f'{location} - 特征重要性')
                        axes[i].grid(True, alpha=0.3)

                        # 添加数值标签
                        for j, v in enumerate(importance_df['importance']):
                            axes[i].text(v + 0.5, j, f'{v:.1f}%', va='center')
                    else:
                        print(f"  数据量不足（{len(rf_data)}行）")
                        axes[i].text(0.5, 0.5, '数据量不足', ha='center', va='center', transform=axes[i].transAxes)
                        axes[i].set_title(f'{location} - 特征重要性 (数据不足)')
                else:
                    print(f"  可用特征不足")
                    axes[i].text(0.5, 0.5, '特征不足', ha='center', va='center', transform=axes[i].transAxes)
                    axes[i].set_title(f'{location} - 特征重要性 (特征不足)')

            plt.tight_layout()
            plt.savefig('特征重要性分析.png', dpi=300, bbox_inches='tight')
            plt.show()

        except ImportError:
            print("  需要安装scikit-learn库进行特征重要性分析")

    def _time_series_decomposition(self):
        """时间序列分解分析"""
        print("\n--- 时间序列分解分析 ---")

        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('室内温度时间序列分解分析', fontsize=16, fontweight='bold')

        for i, (data, location) in enumerate([(self.data_location1, "地点1"), (self.data_location2, "地点2")]):
            if '室内温度均值' not in data.columns or '时间' not in data.columns:
                continue

            # 准备时间序列数据
            ts_data = data[['时间', '室内温度均值']].dropna().copy()
            ts_data = ts_data.sort_values('时间')

            # 重采样为日均值以减少噪声
            ts_data.set_index('时间', inplace=True)
            daily_temp = ts_data.resample('D')['室内温度均值'].mean().dropna()

            if len(daily_temp) > 30:  # 至少需要30天的数据
                # 1. 原始时间序列
                axes[i, 0].plot(daily_temp.index, daily_temp.values, linewidth=1)
                axes[i, 0].set_title(f'{location} - 原始时间序列（日均值）')
                axes[i, 0].set_ylabel('室内温度 (℃)')
                axes[i, 0].grid(True, alpha=0.3)
                axes[i, 0].tick_params(axis='x', rotation=45)

                # 2. 移动平均（趋势）
                window = min(7, len(daily_temp) // 4)  # 7天移动平均或数据长度的1/4
                if window >= 3:
                    trend = daily_temp.rolling(window=window, center=True).mean()
                    axes[i, 1].plot(daily_temp.index, daily_temp.values, alpha=0.3, label='原始数据')
                    axes[i, 1].plot(trend.index, trend.values, linewidth=2, label=f'{window}天移动平均')
                    axes[i, 1].set_title(f'{location} - 趋势分析')
                    axes[i, 1].set_ylabel('室内温度 (℃)')
                    axes[i, 1].legend()
                    axes[i, 1].grid(True, alpha=0.3)
                    axes[i, 1].tick_params(axis='x', rotation=45)

                    # 计算趋势统计
                    trend_change = trend.iloc[-1] - trend.iloc[0] if len(trend) > 1 else 0
                    print(f"\n{location}趋势分析:")
                    print(f"  整体趋势变化: {trend_change:.2f}℃")
                    print(f"  平均温度: {daily_temp.mean():.2f}℃")
                    print(f"  温度标准差: {daily_temp.std():.2f}℃")
                else:
                    axes[i, 1].text(0.5, 0.5, '数据不足\n无法计算趋势', ha='center', va='center', transform=axes[i, 1].transAxes)
                    axes[i, 1].set_title(f'{location} - 趋势分析 (数据不足)')
            else:
                axes[i, 0].text(0.5, 0.5, '数据不足', ha='center', va='center', transform=axes[i, 0].transAxes)
                axes[i, 0].set_title(f'{location} - 原始时间序列 (数据不足)')
                axes[i, 1].text(0.5, 0.5, '数据不足', ha='center', va='center', transform=axes[i, 1].transAxes)
                axes[i, 1].set_title(f'{location} - 趋势分析 (数据不足)')

        plt.tight_layout()
        plt.savefig('时间序列分解分析.png', dpi=300, bbox_inches='tight')
        plt.show()

    def generate_summary_report(self):
        """生成分析总结报告"""
        print("\n" + "="*60)
        print("                  分析总结报告")
        print("="*60)

        for i, (data, location) in enumerate([(self.data_location1, "地点1"), (self.data_location2, "地点2")]):
            print(f"\n【{location}分析结果】")
            print("-" * 30)

            if '室内温度均值' in data.columns:
                temp_stats = data['室内温度均值'].describe()
                print(f"1. 室内温度特征:")
                print(f"   - 平均温度: {temp_stats['mean']:.2f}℃")
                print(f"   - 温度波动范围: {temp_stats['max'] - temp_stats['min']:.2f}℃")
                print(f"   - 温度标准差: {temp_stats['std']:.2f}℃")

                # 舒适度分析
                comfort_range = (data['室内温度均值'] >= 19) & (data['室内温度均值'] <= 21)
                comfort_rate = comfort_range.sum() / len(data) * 100
                print(f"   - 舒适度达标率: {comfort_rate:.1f}%")

            if '室内温度均值' in data.columns and '环境温度(℃)' in data.columns:
                clean_data = data[['室内温度均值', '环境温度(℃)']].dropna()
                if len(clean_data) > 0:
                    correlation = clean_data['环境温度(℃)'].corr(clean_data['室内温度均值'])
                    print(f"\n2. 室内外温度相关性:")
                    print(f"   - 相关系数: {correlation:.3f}")

                    if abs(correlation) > 0.7:
                        strength = "强"
                    elif abs(correlation) > 0.3:
                        strength = "中等"
                    else:
                        strength = "弱"
                    print(f"   - 相关强度: {strength}相关")

            if '热泵功率(kw)' in data.columns:
                power_data = data['热泵功率(kw)'].dropna()
                power_data = power_data[power_data >= 0]
                if len(power_data) > 0:
                    print(f"\n3. 热泵能耗特征:")
                    print(f"   - 平均功率: {power_data.mean():.2f} kW")
                    print(f"   - 最大功率: {power_data.max():.2f} kW")
                    print(f"   - 功率变异系数: {power_data.std()/power_data.mean()*100:.1f}%")

        print("\n【建议与结论】")
        print("-" * 30)
        print("1. 建筑保温性能:")
        print("   - 分析室内温度波动幅度，评估建筑热惰性")
        print("   - 温度波动小的建筑具有更好的保温性能")

        print("\n2. 系统控制优化:")
        print("   - 根据室内外温度相关性调整控制策略")
        print("   - 强相关性表明需要更精确的环境温度补偿")

        print("\n3. 能耗优化方向:")
        print("   - 分析功率与温差关系，优化供回水温度设定")
        print("   - 利用建筑热惰性，在低电价时段预热")

    def run_analysis(self):
        """运行完整分析流程"""
        print("开始空气源热泵数据分析...")

        # 1. 加载数据
        if not self.load_data():
            return

        # 2. 数据预处理
        self.preprocess_data()

        # 3. 室内温度波动规律分析
        self.analyze_temperature_fluctuation()

        # 4. 室内外温度相关性分析
        self.analyze_indoor_outdoor_correlation()

        # 5. 热泵能耗与温差关系分析
        self.analyze_energy_temperature_relationship()

        # 6. 影响因素分析
        self.analyze_influencing_factors()

        # 7. 生成总结报告
        self.generate_summary_report()

        print("\n分析完成！所有图表已保存。")

if __name__ == "__main__":
    analyzer = HeatPumpAnalyzer()
    analyzer.run_analysis()
