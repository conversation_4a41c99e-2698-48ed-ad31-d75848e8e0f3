import pandas as pd
import pathlib

ROOT_ROOT = pathlib.Path(r"data/附件2")          # 总根目录（不要写到“地点1”）

def load_history(history_dir: pathlib.Path, loc_tag: str) -> pd.DataFrame:
    """读取供热历史：地点X_*.xlsx → concat"""
    dfs = []
    pattern = f"{loc_tag}_*.xlsx"               # 例如 地点2_2022-11-15.xlsx
    for f in history_dir.glob(pattern):
        tmp = pd.read_excel(f)
        tmp["时间"] = pd.to_datetime(tmp["时间"])
        dfs.append(tmp)
    hist = (pd.concat(dfs, ignore_index=True)
              .drop_duplicates("时间")
              .sort_values("时间"))
    return hist

def load_indoor(indoor_dir: pathlib.Path) -> pd.DataFrame:
    """读取室内温度：concat → pivot → 行均值"""
    long = []
    for f in indoor_dir.glob("采集点*_*.xlsx"):
        pt_id = f.stem.split("_")[0]            # 采集点1, 采集点2...
        t = pd.read_excel(f, usecols=["采集时间", "测点温度(℃)"])
        t.rename(columns={"采集时间": "时间", "测点温度(℃)": "室内温度"}, inplace=True)
        t["时间"] = pd.to_datetime(t["时间"])
        t["测点编号"] = pt_id
        long.append(t)

    if not long:                                # 该地点无室温文件
        return pd.DataFrame(columns=["时间"])

    long_df = pd.concat(long, ignore_index=True)

    indoor_wide = (long_df.pivot_table(index="时间",
                                       columns="测点编号",
                                       values="室内温度",
                                       aggfunc="mean",
                                       dropna=False)    # 即便整列 NaN 也保留
                     .sort_index()
                     .rename_axis(columns=None)
                     .reset_index())

    temp_cols = indoor_wide.columns.difference(["时间"])
    indoor_wide["室内温度均值"] = indoor_wide[temp_cols].mean(axis=1)

    return indoor_wide

def process_location(loc_dir: pathlib.Path):
    loc_tag = loc_dir.name                       # “地点1” / “地点2”
    history_dir = loc_dir / "供热历史数据"
    indoor_dir  = loc_dir / "室内温度采集数据"
    out_csv     = loc_dir / f"{loc_tag}_完整合并表.csv"

    # ① 读历史
    hist = load_history(history_dir, loc_tag)
    if hist.empty:
        print(f"⚠️ {loc_tag}: 未找到供热历史文件，跳过")
        return

    # ② 读室温
    indoor = load_indoor(indoor_dir)

    # ③ 合并
    full = (pd.merge(hist, indoor, on="时间", how="left")
              .sort_values("时间"))

    # ④ 保存
    full.to_csv(out_csv, index=False, encoding="utf-8-sig")
    print(f"✅ {loc_tag}: 已生成 {out_csv}")

# ========= 主程序：批量遍历 =========
for loc_path in ROOT_ROOT.glob("地点*"):
    if loc_path.is_dir():
        process_location(loc_path)
