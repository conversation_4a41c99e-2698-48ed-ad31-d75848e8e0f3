# 问题1：室内温度波动规律分析 - 使用说明

## 项目概述

本项目针对空气源热泵系统的室内温度波动规律进行全面分析，包含4个子问题的详细分析和可视化结果。

## 文件结构说明

```
问题1_室内温度波动规律分析/
├── 1.1_时间序列分解/                    # 子问题1.1：时间序列分解分析
│   ├── 地点1_时间序列分解图.png          # 地点1时间序列分解（趋势、季节性、残差）
│   ├── 地点1_温度分布图.png              # 地点1温度分布直方图和箱线图
│   ├── 地点1_温度时序图.png              # 地点1温度时间序列变化图
│   ├── 地点2_时间序列分解图.png          # 地点2时间序列分解
│   ├── 地点2_温度分布图.png              # 地点2温度分布图
│   ├── 地点2_温度时序图.png              # 地点2温度时序图
│   └── 温度统计.txt                     # 两地点温度统计分析报告
├── 1.2_温度相关性分析/                  # 子问题1.2：室内外温度相关性
│   ├── 地点1_温度相关性散点图.png        # 地点1室内外温度相关性散点图
│   └── 地点2_温度相关性散点图.png        # 地点2室内外温度相关性散点图
├── 1.3_能耗温差分析/                    # 子问题1.3：热泵能耗与温差关系
│   ├── 地点1_能耗温差散点图.png          # 地点1能耗与温差散点图
│   ├── 地点1_温差区间能耗分布.png        # 地点1不同温差区间的能耗分布
│   ├── 地点2_能耗温差散点图.png          # 地点2能耗与温差散点图
│   ├── 地点2_温差区间能耗分布.png        # 地点2温差区间能耗分布
│   └── 能耗温差分析.txt                 # 能耗与温差定量关系分析报告
├── 1.4_变量相关性分析/                  # 子问题1.4：影响因素相关性分析
│   ├── 地点1_变量相关性热力图.png        # 地点1关键变量相关性热力图
│   ├── 地点1_相关系数矩阵.csv           # 地点1相关系数矩阵数据
│   ├── 地点2_变量相关性热力图.png        # 地点2变量相关性热力图
│   ├── 地点2_相关系数矩阵.csv           # 地点2相关系数矩阵数据
│   └── 相关性分析说明.txt               # 相关性分析方法说明
├── 问题1_综合分析报告.md                # 综合分析报告
└── 使用说明.md                         # 本文件
```

## 分析内容详解

### 1.1 时间序列分解分析

**目标**: 分析室内温度的时间变化规律和波动特征

**输出文件**:
- **时间序列分解图**: 展示温度数据的趋势、季节性和随机波动分量
- **温度分布图**: 直方图显示温度分布特征，箱线图显示统计特征
- **温度时序图**: 室内温度、环境温度、设定温度的时间变化对比
- **温度统计.txt**: 详细的统计指标，包括均值、标准差、分位数、舒适度占比等

**关键指标**:
- 平均温度、温度范围、变异系数
- 舒适温度范围(19-21℃)占比
- 温度稳定性评估

### 1.2 温度相关性分析

**目标**: 研究室内外温度的关联关系

**输出文件**:
- **温度相关性散点图**: 室内温度与环境温度的散点图，包含回归线和相关系数

**分析要点**:
- 相关系数强度评估
- 线性回归关系
- 建筑保温性能评估

### 1.3 能耗温差分析

**目标**: 分析热泵能耗与温差的定量关系

**输出文件**:
- **能耗温差散点图**: 热泵功率与室内外温差的关系图
- **温差区间能耗分布图**: 不同温差区间的平均能耗和数据分布
- **能耗温差分析.txt**: 定量关系分析报告

**分析维度**:
- 能耗与温差的相关性
- 不同温差区间的能耗特征
- 节能优化建议

### 1.4 变量相关性分析

**目标**: 分析影响室内温度的关键因素

**分析变量**:
- 室内温度均值
- 环境温度
- 供温(℃)
- 回温(℃)
- 设定温度(℃)
- 热泵功率(kw)
- 流速(m3h)

**输出文件**:
- **变量相关性热力图**: 变量间相关系数的可视化矩阵
- **相关系数矩阵.csv**: 详细的相关系数数据
- **相关性分析说明.txt**: 相关性解读指南

## 数据质量说明

### 数据来源
- **地点1**: 8,769个数据点
- **地点2**: 8,146个数据点
- **时间范围**: 2022年11月至2025年3月
- **采样频率**: 每小时

### 数据预处理
1. 时间格式标准化
2. 数值型数据清洗
3. 异常值识别和处理
4. 缺失值处理

## 关键发现摘要

### 温度控制效果
- **地点1**: 平均20.48℃，舒适度66.1%，温度稳定性好
- **地点2**: 平均19.66℃，舒适度78.1%，控制精度高

### 影响因素重要性
1. **供回水温度** - 系统核心控制参数
2. **设定温度** - 目标控制参数
3. **热泵功率** - 能耗关键因素
4. **环境温度** - 外部干扰因素

### 节能潜力
- 通过优化温度设定可节能5-10%
- 峰谷电价策略可降低电费成本
- 预测控制可提高系统效率

## 使用建议

### 查看顺序建议
1. 先阅读 **问题1_综合分析报告.md** 了解整体情况
2. 查看 **1.1_时间序列分解/温度统计.txt** 了解基础统计信息
3. 浏览各子文件夹中的图表文件
4. 深入研究 **1.4_变量相关性分析** 中的相关系数矩阵

### 图表解读要点
- **相关系数**: |r| > 0.7为强相关，0.3-0.7为中等相关，< 0.3为弱相关
- **R²值**: 回归模型的拟合优度，越接近1拟合越好
- **温差区间**: 关注不同温差下的能耗变化趋势

### 应用建议
1. **运维优化**: 基于分析结果调整系统参数
2. **节能改造**: 重点关注高能耗温差区间
3. **预测建模**: 利用相关性分析结果建立预测模型
4. **策略制定**: 结合峰谷电价制定运行策略

## 技术说明

### 分析工具
- **Python**: 数据处理和分析
- **pandas**: 数据操作
- **matplotlib/seaborn**: 数据可视化
- **scikit-learn**: 机器学习和统计分析
- **statsmodels**: 时间序列分析

### 统计方法
- 描述性统计分析
- 相关性分析
- 线性回归分析
- 时间序列分解
- 分组统计分析

---

*如有疑问或需要进一步分析，请参考源代码文件：问题1_室内温度波动规律分析.py*
