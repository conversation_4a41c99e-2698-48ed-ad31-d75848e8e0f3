#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
空气源热泵数据分析 - 改进版
重点改进第三个子问题：热泵能耗与温差定量关系分析
添加多种模型对比和详细的模型摘要
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler, PolynomialFeatures
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
# import statsmodels.api as sm  # 如果没有安装statsmodels，我们手动计算统计量
from scipy import stats as scipy_stats
import warnings
import os
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class ImprovedHeatPumpAnalyzer:
    def __init__(self):
        self.data_location1 = None
        self.data_location2 = None
        self.energy_model_results = {}

        # 创建子问题文件夹
        self.folders = [
            "1-室内温度波动规律分析",
            "2-室内外温度相关性分析",
            "3-热泵能耗与温差定量关系分析",
            "4-影响室内温度的影响因素分析"
        ]

        for folder in self.folders:
            if not os.path.exists(folder):
                os.makedirs(folder)
                print(f"创建文件夹: {folder}")

    def load_data(self):
        """加载数据"""
        print("正在加载数据...")
        try:
            # 读取地点1数据
            self.data_location1 = pd.read_csv('../data/附件2/地点1/地点1_完整合并表.csv', encoding='utf-8')
            print(f"地点1数据加载成功，共{len(self.data_location1)}行")

            # 读取地点2数据
            self.data_location2 = pd.read_csv('../data/附件2/地点2/地点2_完整合并表.csv', encoding='utf-8')
            print(f"地点2数据加载成功，共{len(self.data_location2)}行")

            return True
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False

    def preprocess_data(self):
        """数据预处理"""
        print("正在进行数据预处理...")

        for i, (data, location) in enumerate([(self.data_location1, "地点1"), (self.data_location2, "地点2")]):
            print(f"\n处理{location}数据:")

            # 转换时间列
            data['时间'] = pd.to_datetime(data['时间'])

            # 显示可用列
            print(f"可用列: {list(data.columns)}")

            # 数据清洗
            original_len = len(data)
            if '室内温度均值' in data.columns:
                data = data[(data['室内温度均值'] >= 10) & (data['室内温度均值'] <= 35)]

            # 计算室内外温差
            if '室内温度均值' in data.columns and '环境温度(℃)' in data.columns:
                data['室内外温差'] = data['室内温度均值'] - data['环境温度(℃)']

            # 添加时间特征
            data['小时'] = data['时间'].dt.hour
            data['日期'] = data['时间'].dt.date
            data['月份'] = data['时间'].dt.month

            # 更新数据
            if i == 0:
                self.data_location1 = data
            else:
                self.data_location2 = data

            print(f"{location}预处理完成，剩余{len(data)}行数据")

    def analyze_energy_consumption_advanced(self):
        """改进的热泵能耗与温差关系分析 - 多模型对比"""
        print("\n=== 热泵能耗与温差定量关系分析（多模型对比）===")

        # 存储模型结果
        self.energy_model_results = {}

        fig, axes = plt.subplots(3, 2, figsize=(16, 18))
        fig.suptitle('热泵能耗与温差定量关系分析（多模型对比）', fontsize=16, fontweight='bold')

        for i, (data, location) in enumerate([(self.data_location1, "地点1"), (self.data_location2, "地点2")]):
            if '热泵功率(kw)' not in data.columns:
                print(f"{location}缺少热泵功率数据，跳过分析")
                continue

            # 准备特征变量
            feature_cols = ['室内外温差', '环境温度(℃)']
            available_features = [col for col in feature_cols if col in data.columns]

            if len(available_features) == 0:
                print(f"{location}缺少特征变量，跳过分析")
                continue

            # 清理数据
            clean_data = data[available_features + ['热泵功率(kw)']].dropna()
            clean_data = clean_data[clean_data['热泵功率(kw)'] >= 0]  # 移除负功率值

            # 移除异常值（超过99%分位数）
            power_threshold = clean_data['热泵功率(kw)'].quantile(0.99)
            clean_data = clean_data[clean_data['热泵功率(kw)'] <= power_threshold]

            if len(clean_data) < 100:
                print(f"{location}数据不足（<100行），跳过模型分析")
                continue

            # 准备特征和目标变量
            X = clean_data[available_features]
            y = clean_data['热泵功率(kw)']

            # 分割训练和测试集
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

            # 存储该地点的模型结果
            location_results = {
                'data_points': len(clean_data),
                'train_size': len(X_train),
                'test_size': len(X_test),
                'features': available_features,
                'power_stats': {
                    'mean': y.mean(),
                    'std': y.std(),
                    'min': y.min(),
                    'max': y.max(),
                    'cv': y.std() / y.mean() * 100
                },
                'models': {}
            }

            # 1. 线性回归模型
            print(f"\n{location} - 训练线性回归模型...")
            lr_model = LinearRegression()
            lr_model.fit(X_train, y_train)
            y_pred_lr = lr_model.predict(X_test)

            lr_r2 = r2_score(y_test, y_pred_lr)
            lr_rmse = np.sqrt(mean_squared_error(y_test, y_pred_lr))
            lr_mae = mean_absolute_error(y_test, y_pred_lr)

            # 计算详细的回归统计量
            lr_stats = self._calculate_regression_stats(X_train, y_train, lr_model)

            location_results['models']['线性回归'] = {
                'R²': lr_r2,
                'RMSE': lr_rmse,
                'MAE': lr_mae,
                'coefficients': dict(zip(available_features, lr_model.coef_)),
                'intercept': lr_model.intercept_,
                'equation': self._generate_equation(lr_model.coef_, lr_model.intercept_, available_features),
                'detailed_stats': lr_stats,
                'summary_table': self._format_regression_summary(lr_stats, f"{location} 线性回归")
            }

            # 2. 多项式回归模型（2次）
            print(f"{location} - 训练多项式回归模型...")
            poly_features = PolynomialFeatures(degree=2, include_bias=False)
            X_train_poly = poly_features.fit_transform(X_train)
            X_test_poly = poly_features.transform(X_test)

            poly_model = LinearRegression()
            poly_model.fit(X_train_poly, y_train)
            y_pred_poly = poly_model.predict(X_test_poly)

            poly_r2 = r2_score(y_test, y_pred_poly)
            poly_rmse = np.sqrt(mean_squared_error(y_test, y_pred_poly))
            poly_mae = mean_absolute_error(y_test, y_pred_poly)

            location_results['models']['多项式回归'] = {
                'R²': poly_r2,
                'RMSE': poly_rmse,
                'MAE': poly_mae,
                'degree': 2,
                'feature_names': poly_features.get_feature_names_out(available_features).tolist(),
                'n_features': X_train_poly.shape[1]
            }

            # 3. 随机森林模型
            print(f"{location} - 训练随机森林模型...")
            rf_model = RandomForestRegressor(n_estimators=100, random_state=42, max_depth=10)
            rf_model.fit(X_train, y_train)
            y_pred_rf = rf_model.predict(X_test)

            rf_r2 = r2_score(y_test, y_pred_rf)
            rf_rmse = np.sqrt(mean_squared_error(y_test, y_pred_rf))
            rf_mae = mean_absolute_error(y_test, y_pred_rf)

            # 特征重要性
            feature_importance = dict(zip(available_features, rf_model.feature_importances_))

            location_results['models']['随机森林'] = {
                'R²': rf_r2,
                'RMSE': rf_rmse,
                'MAE': rf_mae,
                'feature_importance': feature_importance,
                'n_estimators': 100,
                'max_depth': 10,
                'oob_score': getattr(rf_model, 'oob_score_', None)
            }

            # 绘制结果对比
            self._plot_model_comparison(axes, i, location, y_test, y_pred_lr, y_pred_poly, y_pred_rf,
                                      lr_r2, poly_r2, rf_r2, clean_data, available_features)

            # 保存该地点的结果
            self.energy_model_results[location] = location_results

            # 打印详细模型摘要
            self._print_model_summary(location, location_results)

            # 打印线性回归的详细统计表格
            if 'summary_table' in location_results['models']['线性回归']:
                print(location_results['models']['线性回归']['summary_table'])

        plt.tight_layout()

        # 保存图片到对应文件夹
        save_path = os.path.join(self.folders[2], '热泵能耗与温差定量关系分析_多模型对比.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

        # 生成详细的模型摘要报告
        self._generate_advanced_energy_report()

    def _generate_equation(self, coef, intercept, features):
        """生成回归方程字符串"""
        equation = f"功率 = {intercept:.3f}"
        for i, feature in enumerate(features):
            if coef[i] >= 0:
                equation += f" + {coef[i]:.3f}×{feature}"
            else:
                equation += f" - {abs(coef[i]):.3f}×{feature}"
        return equation

    def _calculate_regression_stats(self, X, y, model):
        """计算详细的回归统计量"""
        # 预测值
        y_pred = model.predict(X)

        # 残差
        residuals = y - y_pred

        # 自由度
        n = len(y)  # 样本数
        p = X.shape[1]  # 特征数
        df_resid = n - p - 1  # 残差自由度
        df_model = p  # 模型自由度

        # 残差平方和
        sse = np.sum(residuals**2)

        # 总平方和
        tss = np.sum((y - np.mean(y))**2)

        # 回归平方和
        mse = sse / df_resid  # 均方误差

        # 计算协方差矩阵
        # X_with_intercept = np.column_stack([np.ones(n), X])  # 添加截距列
        X_with_intercept = np.column_stack([np.ones(n), X])

        try:
            # 计算 (X'X)^(-1)
            XtX_inv = np.linalg.inv(X_with_intercept.T @ X_with_intercept)

            # 参数的标准误差
            var_coef = mse * np.diag(XtX_inv)
            std_err = np.sqrt(var_coef)

            # 系数（包括截距）
            coefficients = np.concatenate([[model.intercept_], model.coef_])

            # t统计量
            t_stats = coefficients / std_err

            # p值（双尾检验）
            p_values = 2 * (1 - scipy_stats.t.cdf(np.abs(t_stats), df_resid))

            # 95%置信区间
            t_critical = scipy_stats.t.ppf(0.975, df_resid)
            conf_lower = coefficients - t_critical * std_err
            conf_upper = coefficients + t_critical * std_err

            # R²
            r_squared = 1 - sse / tss

            # 调整R²
            adj_r_squared = 1 - (sse / df_resid) / (tss / (n - 1))

            # F统计量
            msr = (tss - sse) / df_model  # 回归均方
            f_stat = msr / mse
            f_pvalue = 1 - scipy_stats.f.cdf(f_stat, df_model, df_resid)

            return {
                'coefficients': coefficients,
                'std_err': std_err,
                't_stats': t_stats,
                'p_values': p_values,
                'conf_lower': conf_lower,
                'conf_upper': conf_upper,
                'r_squared': r_squared,
                'adj_r_squared': adj_r_squared,
                'f_stat': f_stat,
                'f_pvalue': f_pvalue,
                'df_model': df_model,
                'df_resid': df_resid,
                'n_obs': n,
                'mse': mse,
                'sse': sse,
                'feature_names': ['const'] + list(X.columns) if hasattr(X, 'columns') else ['const'] + [f'x{i}' for i in range(X.shape[1])]
            }

        except np.linalg.LinAlgError:
            # 如果矩阵不可逆，返回简化版本
            return {
                'coefficients': np.concatenate([[model.intercept_], model.coef_]),
                'r_squared': r2_score(y, y_pred),
                'feature_names': ['const'] + list(X.columns) if hasattr(X, 'columns') else ['const'] + [f'x{i}' for i in range(X.shape[1])],
                'error': 'Matrix inversion failed - simplified stats only'
            }

    def _format_regression_summary(self, stats_dict, model_name):
        """格式化回归摘要表格"""
        if 'error' in stats_dict:
            return f"\n{model_name} 回归摘要:\n错误: {stats_dict['error']}\n"

        summary = f"\n{'='*80}\n"
        summary += f"{model_name} 回归摘要\n"
        summary += f"{'='*80}\n"

        # 模型信息
        summary += f"Dep. Variable:           热泵功率(kw)    R-squared:           {stats_dict['r_squared']:.4f}\n"
        summary += f"Model:                   OLS             Adj. R-squared:      {stats_dict['adj_r_squared']:.4f}\n"
        summary += f"No. Observations:        {stats_dict['n_obs']}           F-statistic:         {stats_dict['f_stat']:.4f}\n"
        summary += f"Df Residuals:            {stats_dict['df_resid']}           Prob (F-statistic):  {stats_dict['f_pvalue']:.3e}\n"
        summary += f"Df Model:                {stats_dict['df_model']}           \n"
        summary += f"Covariance Type:         nonrobust       \n"
        summary += f"{'='*80}\n"

        # 系数表格
        summary += f"{'Variable':<20} {'coef':<12} {'std err':<12} {'t':<8} {'P>|t|':<8} {'[0.025':<8} {'0.975]':<8}\n"
        summary += f"{'-'*80}\n"

        for i, var_name in enumerate(stats_dict['feature_names']):
            coef = stats_dict['coefficients'][i]
            std_err = stats_dict['std_err'][i]
            t_stat = stats_dict['t_stats'][i]
            p_val = stats_dict['p_values'][i]
            conf_low = stats_dict['conf_lower'][i]
            conf_high = stats_dict['conf_upper'][i]

            summary += f"{var_name:<20} {coef:<12.4f} {std_err:<12.4f} {t_stat:<8.3f} {p_val:<8.3f} {conf_low:<8.3f} {conf_high:<8.3f}\n"

        summary += f"{'='*80}\n"

        # 诊断信息
        summary += f"Omnibus:                 N/A             Durbin-Watson:       N/A\n"
        summary += f"Prob(Omnibus):           N/A             Jarque-Bera (JB):    N/A\n"
        summary += f"Skew:                    N/A             Prob(JB):            N/A\n"
        summary += f"Kurtosis:                N/A             Cond. No.            N/A\n"
        summary += f"{'='*80}\n"

        return summary

    def _plot_model_comparison(self, axes, i, location, y_test, y_pred_lr, y_pred_poly, y_pred_rf,
                              lr_r2, poly_r2, rf_r2, clean_data, available_features):
        """绘制模型对比图"""

        # 第一行：实际vs预测散点图
        axes[0, i].scatter(y_test, y_pred_lr, alpha=0.6, s=15, label=f'线性回归 (R²={lr_r2:.3f})', color='blue')
        axes[0, i].scatter(y_test, y_pred_poly, alpha=0.6, s=15, label=f'多项式回归 (R²={poly_r2:.3f})', color='orange')
        axes[0, i].scatter(y_test, y_pred_rf, alpha=0.6, s=15, label=f'随机森林 (R²={rf_r2:.3f})', color='green')

        # 理想线
        min_val, max_val = y_test.min(), y_test.max()
        axes[0, i].plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8, linewidth=2, label='理想线')
        axes[0, i].set_title(f'{location} - 模型预测对比')
        axes[0, i].set_xlabel('实际功率 (kW)')
        axes[0, i].set_ylabel('预测功率 (kW)')
        axes[0, i].legend()
        axes[0, i].grid(True, alpha=0.3)

        # 第二行：功率与主要特征关系
        if '室内外温差' in available_features:
            temp_diff = clean_data['室内外温差']
            power = clean_data['热泵功率(kw)']

            axes[1, i].scatter(temp_diff, power, alpha=0.6, s=15, color='purple')

            # 添加线性回归线
            z = np.polyfit(temp_diff, power, 1)
            p = np.poly1d(z)
            x_range = np.linspace(temp_diff.min(), temp_diff.max(), 100)
            axes[1, i].plot(x_range, p(x_range), "r--", alpha=0.8, linewidth=2)

            correlation = temp_diff.corr(power)
            axes[1, i].set_title(f'{location} - 功率vs温差 (r={correlation:.3f})')
            axes[1, i].set_xlabel('室内外温差 (℃)')
            axes[1, i].set_ylabel('热泵功率 (kW)')
            axes[1, i].grid(True, alpha=0.3)

        # 第三行：模型性能对比
        models = ['线性回归', '多项式回归', '随机森林']
        r2_scores = [lr_r2, poly_r2, rf_r2]

        x_pos = np.arange(len(models))
        bars = axes[2, i].bar(x_pos, r2_scores, alpha=0.7, color=['blue', 'orange', 'green'])
        axes[2, i].set_title(f'{location} - 模型R²对比')
        axes[2, i].set_xlabel('模型')
        axes[2, i].set_ylabel('R²值')
        axes[2, i].set_xticks(x_pos)
        axes[2, i].set_xticklabels(models, rotation=45)
        axes[2, i].grid(True, alpha=0.3)

        # 在柱状图上添加数值标签
        for j, v in enumerate(r2_scores):
            axes[2, i].text(j, v + 0.01, f'{v:.3f}', ha='center', va='bottom')

    def _print_model_summary(self, location, results):
        """打印详细模型摘要"""
        print(f"\n{'='*50}")
        print(f"{location} 模型摘要")
        print(f"{'='*50}")

        print(f"数据概况:")
        print(f"  总数据点: {results['data_points']}")
        print(f"  训练集: {results['train_size']}")
        print(f"  测试集: {results['test_size']}")
        print(f"  特征变量: {', '.join(results['features'])}")

        power_stats = results['power_stats']
        print(f"\n功率统计:")
        print(f"  平均功率: {power_stats['mean']:.2f} kW")
        print(f"  功率标准差: {power_stats['std']:.2f} kW")
        print(f"  功率范围: {power_stats['min']:.2f} - {power_stats['max']:.2f} kW")
        print(f"  变异系数: {power_stats['cv']:.1f}%")

        print(f"\n模型性能对比:")
        for model_name, metrics in results['models'].items():
            print(f"  {model_name}:")
            print(f"    R²: {metrics['R²']:.4f}")
            print(f"    RMSE: {metrics['RMSE']:.2f} kW")
            print(f"    MAE: {metrics['MAE']:.2f} kW")

            if model_name == '线性回归':
                print(f"    回归方程: {metrics['equation']}")
                print(f"    系数: {metrics['coefficients']}")
            elif model_name == '多项式回归':
                print(f"    多项式次数: {metrics['degree']}")
                print(f"    特征数量: {metrics['n_features']}")
            elif model_name == '随机森林':
                print(f"    特征重要性: {metrics['feature_importance']}")
                print(f"    树的数量: {metrics['n_estimators']}")
                print(f"    最大深度: {metrics['max_depth']}")

        # 最佳模型
        best_model = max(results['models'].items(), key=lambda x: x[1]['R²'])
        print(f"\n最佳模型: {best_model[0]} (R²={best_model[1]['R²']:.4f})")

    def _generate_advanced_energy_report(self):
        """生成改进的能耗分析报告"""
        report_path = os.path.join(self.folders[2], '热泵能耗与温差定量关系分析_详细模型摘要.txt')

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("热泵能耗与温差定量关系分析 - 详细模型摘要报告\n")
            f.write("="*70 + "\n\n")

            f.write("一、分析目的\n")
            f.write("-"*30 + "\n")
            f.write("建立热泵功率与温差的定量关系模型，包括：\n")
            f.write("1. 多种机器学习模型对比（线性回归、多项式回归、随机森林）\n")
            f.write("2. 详细的模型性能评估和参数分析\n")
            f.write("3. 特征重要性分析\n")
            f.write("4. 模型适用性评估和选择建议\n\n")

            f.write("二、使用的模型\n")
            f.write("-"*30 + "\n")
            f.write("1. 线性回归 (Linear Regression):\n")
            f.write("   - 原理: 假设功率与特征变量存在线性关系\n")
            f.write("   - 优点: 简单、可解释性强、计算快速\n")
            f.write("   - 缺点: 无法捕捉非线性关系\n")
            f.write("   - 适用场景: 变量间关系相对简单的情况\n\n")

            f.write("2. 多项式回归 (Polynomial Regression):\n")
            f.write("   - 原理: 通过多项式特征扩展捕捉非线性关系\n")
            f.write("   - 优点: 能捕捉一定的非线性关系，仍具有较好的可解释性\n")
            f.write("   - 缺点: 容易过拟合，特征数量增长快\n")
            f.write("   - 适用场景: 存在明显非线性但不太复杂的关系\n\n")

            f.write("3. 随机森林 (Random Forest):\n")
            f.write("   - 原理: 集成多个决策树，通过投票或平均得到最终结果\n")
            f.write("   - 优点: 能处理复杂非线性关系，抗过拟合能力强，提供特征重要性\n")
            f.write("   - 缺点: 可解释性相对较差，计算复杂度高\n")
            f.write("   - 适用场景: 复杂的非线性关系，特征较多的情况\n\n")

            f.write("三、数据处理\n")
            f.write("-"*30 + "\n")
            f.write("1. 数据清洗:\n")
            f.write("   - 移除负功率值（物理上不合理）\n")
            f.write("   - 移除缺失值\n")
            f.write("   - 移除异常值（超过99%分位数的极值）\n\n")
            f.write("2. 特征工程:\n")
            f.write("   - 计算室内外温差\n")
            f.write("   - 选择相关性较强的特征变量\n\n")
            f.write("3. 数据分割:\n")
            f.write("   - 训练集: 80%\n")
            f.write("   - 测试集: 20%\n")
            f.write("   - 随机种子: 42（确保结果可重现）\n\n")

            f.write("四、评估指标说明\n")
            f.write("-"*30 + "\n")
            f.write("1. R²（决定系数）:\n")
            f.write("   - 范围: 0-1，越接近1越好\n")
            f.write("   - 含义: 模型解释的方差比例\n")
            f.write("   - 评判标准: >0.7优秀，0.5-0.7良好，0.3-0.5一般，<0.3较差\n\n")
            f.write("2. RMSE（均方根误差）:\n")
            f.write("   - 单位: kW\n")
            f.write("   - 含义: 预测值与真实值的平均偏差\n")
            f.write("   - 评判标准: 越小越好，相对于功率均值的比例<10%为优秀\n\n")
            f.write("3. MAE（平均绝对误差）:\n")
            f.write("   - 单位: kW\n")
            f.write("   - 含义: 预测值与真实值绝对偏差的平均值\n")
            f.write("   - 评判标准: 越小越好，对异常值不敏感\n\n")

            f.write("五、详细分析结果\n")
            f.write("-"*30 + "\n")

            # 详细分析结果
            for location, results in self.energy_model_results.items():
                f.write(f"\n【{location}分析结果】\n")
                f.write(f"数据概况:\n")
                f.write(f"  总数据点: {results['data_points']}\n")
                f.write(f"  训练集大小: {results['train_size']}\n")
                f.write(f"  测试集大小: {results['test_size']}\n")
                f.write(f"  特征变量: {', '.join(results['features'])}\n")

                power_stats = results['power_stats']
                f.write(f"\n功率统计特征:\n")
                f.write(f"  平均功率: {power_stats['mean']:.2f} kW\n")
                f.write(f"  功率标准差: {power_stats['std']:.2f} kW\n")
                f.write(f"  功率范围: {power_stats['min']:.2f} - {power_stats['max']:.2f} kW\n")
                f.write(f"  变异系数: {power_stats['cv']:.1f}%\n")

                f.write(f"\n模型性能详细对比:\n")
                for model_name, metrics in results['models'].items():
                    f.write(f"\n{model_name}:\n")
                    f.write(f"  R²: {metrics['R²']:.4f}\n")
                    f.write(f"  RMSE: {metrics['RMSE']:.2f} kW\n")
                    f.write(f"  MAE: {metrics['MAE']:.2f} kW\n")
                    f.write(f"  相对RMSE: {metrics['RMSE']/power_stats['mean']*100:.1f}%\n")

                    if model_name == '线性回归':
                        f.write(f"  回归方程: {metrics['equation']}\n")
                        f.write(f"  系数解释:\n")
                        for feature, coef in metrics['coefficients'].items():
                            f.write(f"    {feature}: {coef:.3f} (每增加1单位，功率变化{coef:.3f}kW)\n")
                        f.write(f"  截距: {metrics['intercept']:.3f} kW\n")

                        # 添加详细的统计表格
                        if 'summary_table' in metrics:
                            f.write(f"\n  详细回归统计表格:\n")
                            f.write(metrics['summary_table'])
                            f.write(f"\n")

                    elif model_name == '多项式回归':
                        f.write(f"  多项式次数: {metrics['degree']}\n")
                        f.write(f"  扩展后特征数: {metrics['n_features']}\n")
                        f.write(f"  特征名称: {', '.join(metrics['feature_names'])}\n")

                    elif model_name == '随机森林':
                        f.write(f"  模型参数:\n")
                        f.write(f"    树的数量: {metrics['n_estimators']}\n")
                        f.write(f"    最大深度: {metrics['max_depth']}\n")
                        f.write(f"  特征重要性排序:\n")
                        sorted_importance = sorted(metrics['feature_importance'].items(),
                                                 key=lambda x: x[1], reverse=True)
                        for feature, importance in sorted_importance:
                            f.write(f"    {feature}: {importance:.3f} ({importance*100:.1f}%)\n")

                # 最佳模型推荐
                best_model = max(results['models'].items(), key=lambda x: x[1]['R²'])
                f.write(f"\n推荐模型: {best_model[0]}\n")
                f.write(f"  R²: {best_model[1]['R²']:.4f}\n")
                f.write(f"  RMSE: {best_model[1]['RMSE']:.2f} kW\n")
                f.write(f"  推荐理由: 在测试集上表现最佳\n")

            f.write(f"\n六、模型选择建议\n")
            f.write("-"*30 + "\n")
            f.write("1. 模型性能评估:\n")
            f.write("   - 优先考虑R²值最高的模型\n")
            f.write("   - 同时关注RMSE和MAE，确保预测精度\n")
            f.write("   - 避免过拟合，测试集性能应接近训练集\n\n")

            f.write("2. 实际应用考虑:\n")
            f.write("   - 如需要可解释性，优选线性回归\n")
            f.write("   - 如追求预测精度，可选择随机森林\n")
            f.write("   - 多项式回归适合中等复杂度的关系\n\n")

            f.write("3. 模型部署建议:\n")
            f.write("   - 定期重新训练模型，适应系统变化\n")
            f.write("   - 建立模型监控机制，及时发现性能下降\n")
            f.write("   - 结合领域知识，验证模型预测的合理性\n\n")

            f.write("4. 进一步改进方向:\n")
            f.write("   - 增加更多特征变量（如历史功率、天气数据等）\n")
            f.write("   - 尝试其他机器学习算法（如XGBoost、神经网络等）\n")
            f.write("   - 考虑时间序列特征，建立动态预测模型\n")
            f.write("   - 进行特征选择和降维，提高模型效率\n")

        print(f"已生成详细模型摘要报告：{report_path}")

    def run_improved_analysis(self):
        """运行改进的分析"""
        print("开始改进版空气源热泵数据分析...")

        # 1. 加载数据
        if not self.load_data():
            return

        # 2. 数据预处理
        self.preprocess_data()

        # 3. 改进的能耗分析
        self.analyze_energy_consumption_advanced()

        print("\n改进版分析完成！")

if __name__ == "__main__":
    analyzer = ImprovedHeatPumpAnalyzer()
    analyzer.run_improved_analysis()
