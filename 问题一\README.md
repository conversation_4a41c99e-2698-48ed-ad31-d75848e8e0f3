# 问题一：空气源热泵数据分析

## 问题描述
统计所给不同建筑的室内温度波动规律；绘制室内外温度相关性曲线，分析热泵能耗与温差的定量关系；分析影响室内温度的影响因素。

## 文件说明

### 核心文件
- `问题一_空气源热泵数据分析.py` - 主要分析脚本
- `问题一_解决方案总结.md` - 详细分析报告

### 生成的图表
- `室内温度波动规律分析.png` - 温度波动规律分析图
- `室内外温度相关性分析.png` - 室内外温度相关性图
- `热泵能耗与温差关系分析.png` - 能耗与温差关系图
- `影响因素分析.png` - 影响因素分析图

## 运行方法
```bash
python 问题一_空气源热泵数据分析.py
```

## 主要发现

### 1. 温度波动规律
- **地点1**: 平均20.52℃，标准差0.96℃，舒适度达标率66.8%
- **地点2**: 平均19.72℃，标准差1.01℃，舒适度达标率79.5%

### 2. 室内外温度相关性
- **地点1**: 相关系数0.068 (弱正相关)
- **地点2**: 相关系数-0.134 (弱负相关)
- 两地点建筑保温性能都很好

### 3. 能耗与温差关系
- **地点1**: 平均功率313.67kW，能耗相对稳定
- **地点2**: 平均功率204.30kW，但存在功率异常

### 4. 主要影响因素
- 时间因素（日内周期性变化）
- 设定温度
- 环境温度（间接影响）

## 结论
1. 两栋建筑都具有良好的保温性能
2. 地点2的控制策略更优，舒适度达标率更高
3. 可以利用建筑热惰性进行峰谷电价优化
4. 需要建立异常检测机制，及时发现设备故障

详细分析结果请查看 `问题一_解决方案总结.md`
