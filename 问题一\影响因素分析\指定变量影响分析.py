#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
指定变量对室内温度影响分析
分析变量：平均室内温度（℃）、环境温度（℃）、供温（℃）、回温（℃）、设定温度（℃）、热泵功率（kw）、流速（m3h）
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score, mean_squared_error
from sklearn.model_selection import train_test_split
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class SpecifiedVariablesAnalyzer:
    def __init__(self):
        self.data_location1 = None
        self.data_location2 = None
        self.specified_vars = {
            '室内温度均值': '平均室内温度',
            '环境温度(℃)': '环境温度',
            '供温(℃)': '供温',
            '回温(℃)': '回温',
            '设定温度(℃)': '设定温度',
            '热泵功率(kw)': '热泵功率',
            '流速(m3h)': '流速'
        }

    def load_data(self):
        """加载数据"""
        print("正在加载数据...")
        try:
            # 读取地点1数据
            self.data_location1 = pd.read_csv('../../data/附件2/地点1/地点1_完整合并表.csv', encoding='utf-8')
            print(f"地点1数据加载成功，共{len(self.data_location1)}行")

            # 读取地点2数据
            self.data_location2 = pd.read_csv('../../data/附件2/地点2/地点2_完整合并表.csv', encoding='utf-8')
            print(f"地点2数据加载成功，共{len(self.data_location2)}行")

        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
        return True

    def preprocess_data(self):
        """数据预处理"""
        print("正在进行数据预处理...")

        for i, (data, location) in enumerate([(self.data_location1, "地点1"), (self.data_location2, "地点2")]):
            print(f"\n处理{location}数据:")

            # 转换时间列
            if '时间' in data.columns:
                data['时间'] = pd.to_datetime(data['时间'])

            # 检查指定变量是否存在
            available_vars = [col for col in self.specified_vars.keys() if col in data.columns]
            print(f"可用的指定变量: {available_vars}")

            # 数据清洗 - 移除明显异常值
            if '室内温度均值' in data.columns:
                data = data[(data['室内温度均值'] >= 10) & (data['室内温度均值'] <= 35)]

            if '环境温度(℃)' in data.columns:
                data = data[(data['环境温度(℃)'] >= -20) & (data['环境温度(℃)'] <= 40)]

            if '热泵功率(kw)' in data.columns:
                data = data[data['热泵功率(kw)'] >= 0]  # 移除负功率值

            # 更新数据
            if i == 0:
                self.data_location1 = data
            else:
                self.data_location2 = data

            print(f"{location}预处理完成，剩余{len(data)}行数据")

    def analyze_individual_variables(self):
        """分析各个变量对室内温度的单独影响"""
        print("\n=== 各变量对室内温度的单独影响分析 ===")

        # 创建子图
        n_vars = len(self.specified_vars) - 1  # 除去室内温度本身
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('指定变量对室内温度的影响分析', fontsize=16, fontweight='bold')

        results = {}

        for i, (data, location) in enumerate([(self.data_location1, "地点1"), (self.data_location2, "地点2")]):
            results[location] = {}

            plot_idx = 0
            for var_col, var_name in self.specified_vars.items():
                if var_col == '室内温度均值' or var_col not in data.columns:
                    continue

                if plot_idx >= 3:  # 每行最多3个图
                    break

                row = i
                col = plot_idx

                # 清理数据
                clean_data = data[['室内温度均值', var_col]].dropna()

                if len(clean_data) > 50:
                    # 散点图
                    axes[row, col].scatter(clean_data[var_col], clean_data['室内温度均值'],
                                         alpha=0.6, s=15)

                    # 计算相关系数
                    correlation = clean_data[var_col].corr(clean_data['室内温度均值'])

                    # 拟合趋势线
                    if len(clean_data) > 10:
                        z = np.polyfit(clean_data[var_col], clean_data['室内温度均值'], 1)
                        p = np.poly1d(z)
                        x_range = np.linspace(clean_data[var_col].min(),
                                            clean_data[var_col].max(), 100)
                        axes[row, col].plot(x_range, p(x_range), "r--", alpha=0.8, linewidth=2)

                    axes[row, col].set_title(f'{location} - {var_name}\n(r={correlation:.3f})')
                    axes[row, col].set_xlabel(f'{var_name}')
                    axes[row, col].set_ylabel('室内温度 (℃)')
                    axes[row, col].grid(True, alpha=0.3)

                    # 保存结果
                    results[location][var_name] = {
                        '相关系数': correlation,
                        '数据点数': len(clean_data),
                        '变量范围': f"{clean_data[var_col].min():.2f} - {clean_data[var_col].max():.2f}",
                        '影响程度': '强影响' if abs(correlation) > 0.5 else ('中等影响' if abs(correlation) > 0.3 else '弱影响')
                    }
                else:
                    axes[row, col].text(0.5, 0.5, f'数据不足\n({len(clean_data)}个点)',
                                      ha='center', va='center', transform=axes[row, col].transAxes)
                    axes[row, col].set_title(f'{location} - {var_name} (数据不足)')

                    results[location][var_name] = {
                        '相关系数': 'N/A',
                        '数据点数': len(clean_data),
                        '变量范围': 'N/A',
                        '影响程度': '数据不足'
                    }

                plot_idx += 1

            # 隐藏多余的子图
            for remaining_col in range(plot_idx, 3):
                axes[row, remaining_col].set_visible(False)

        plt.tight_layout()
        plt.savefig('各变量单独影响分析.png', dpi=300, bbox_inches='tight')
        plt.show()

        return results

    def correlation_matrix_analysis(self):
        """相关性矩阵分析"""
        print("\n=== 指定变量相关性矩阵分析 ===")

        fig, axes = plt.subplots(1, 2, figsize=(16, 8))
        fig.suptitle('指定变量相关性矩阵分析', fontsize=16, fontweight='bold')

        correlation_results = {}

        for i, (data, location) in enumerate([(self.data_location1, "地点1"), (self.data_location2, "地点2")]):
            # 筛选存在的列
            available_cols = [col for col in self.specified_vars.keys() if col in data.columns]

            if len(available_cols) > 2:
                corr_data = data[available_cols].dropna()

                if len(corr_data) > 0:
                    # 计算相关性矩阵
                    corr_matrix = corr_data.corr()

                    # 绘制热力图
                    im = axes[i].imshow(corr_matrix.values, cmap='RdBu_r', vmin=-1, vmax=1)

                    # 设置标签
                    axes[i].set_xticks(range(len(corr_matrix.columns)))
                    axes[i].set_yticks(range(len(corr_matrix.columns)))

                    # 简化列名用于显示
                    display_names = [self.specified_vars[col] for col in corr_matrix.columns]

                    axes[i].set_xticklabels(display_names, rotation=45, ha='right')
                    axes[i].set_yticklabels(display_names)

                    # 添加数值标注
                    for row in range(len(corr_matrix.columns)):
                        for col in range(len(corr_matrix.columns)):
                            value = corr_matrix.iloc[row, col]
                            color = 'white' if abs(value) > 0.5 else 'black'
                            axes[i].text(col, row, f'{value:.2f}',
                                       ha='center', va='center', color=color, fontsize=10)

                    axes[i].set_title(f'{location} - 指定变量相关性矩阵')

                    # 保存相关性结果
                    correlation_results[location] = {}
                    if '室内温度均值' in corr_matrix.columns:
                        indoor_temp_corr = corr_matrix['室内温度均值'].drop('室内温度均值')
                        correlation_results[location]['与室内温度的相关性'] = {}
                        for var, corr_val in indoor_temp_corr.items():
                            var_name = self.specified_vars[var]
                            correlation_results[location]['与室内温度的相关性'][var_name] = corr_val

                        # 变量间相关性
                        correlation_results[location]['变量间强相关性'] = {}
                        for col1 in corr_matrix.columns:
                            for col2 in corr_matrix.columns:
                                if col1 != col2 and col1 != '室内温度均值' and col2 != '室内温度均值':
                                    corr_val = corr_matrix.loc[col1, col2]
                                    if abs(corr_val) > 0.5:
                                        name1 = self.specified_vars[col1]
                                        name2 = self.specified_vars[col2]
                                        correlation_results[location]['变量间强相关性'][f'{name1} vs {name2}'] = corr_val
                else:
                    axes[i].text(0.5, 0.5, '数据不足', ha='center', va='center', transform=axes[i].transAxes)
                    axes[i].set_title(f'{location} - 相关性矩阵 (数据不足)')
            else:
                axes[i].text(0.5, 0.5, '可用变量不足', ha='center', va='center', transform=axes[i].transAxes)
                axes[i].set_title(f'{location} - 相关性矩阵 (变量不足)')

        # 添加颜色条
        plt.colorbar(im, ax=axes, shrink=0.8, label='相关系数')
        plt.tight_layout()
        plt.savefig('指定变量相关性矩阵.png', dpi=300, bbox_inches='tight')
        plt.show()

        return correlation_results

    def multiple_regression_analysis(self):
        """多元回归分析"""
        print("\n=== 指定变量多元回归分析 ===")

        regression_results = {}

        # 特征变量（除了室内温度）
        feature_cols = [col for col in self.specified_vars.keys() if col != '室内温度均值']

        for i, (data, location) in enumerate([(self.data_location1, "地点1"), (self.data_location2, "地点2")]):
            print(f"\n{location}多元回归分析:")
            regression_results[location] = {}

            # 筛选可用特征
            available_features = [col for col in feature_cols if col in data.columns]

            if len(available_features) >= 2 and '室内温度均值' in data.columns:
                # 准备数据
                regression_data = data[available_features + ['室内温度均值']].dropna()

                if len(regression_data) > 50:
                    X = regression_data[available_features]
                    y = regression_data['室内温度均值']

                    # 标准化特征
                    scaler = StandardScaler()
                    X_scaled = scaler.fit_transform(X)

                    # 拟合回归模型
                    model = LinearRegression()
                    model.fit(X_scaled, y)

                    # 预测和评估
                    y_pred = model.predict(X_scaled)
                    r2 = r2_score(y, y_pred)
                    rmse = np.sqrt(mean_squared_error(y, y_pred))

                    print(f"  模型性能:")
                    print(f"    R² 得分: {r2:.4f}")
                    print(f"    RMSE: {rmse:.4f}℃")
                    print(f"    数据点数: {len(regression_data)}")

                    # 保存模型性能
                    regression_results[location]['模型性能'] = {
                        'R²得分': r2,
                        'RMSE': rmse,
                        '数据点数': len(regression_data)
                    }

                    # 特征系数分析
                    print(f"  变量影响系数（标准化后）:")
                    coef_dict = {}
                    for feature, coef in zip(available_features, model.coef_):
                        var_name = self.specified_vars[feature]
                        coef_dict[var_name] = coef
                        print(f"    {var_name}: {coef:.4f}")

                    print(f"  截距: {model.intercept_:.4f}")

                    # 计算特征贡献度
                    feature_importance = np.abs(model.coef_)
                    feature_importance_norm = feature_importance / np.sum(feature_importance) * 100

                    print(f"  变量重要性排序（按贡献度）:")
                    importance_pairs = list(zip(available_features, feature_importance_norm))
                    importance_pairs.sort(key=lambda x: x[1], reverse=True)

                    importance_dict = {}
                    for feature, importance in importance_pairs:
                        var_name = self.specified_vars[feature]
                        importance_dict[var_name] = importance
                        print(f"    {var_name}: {importance:.2f}%")

                    # 保存系数和重要性
                    regression_results[location]['变量系数'] = coef_dict
                    regression_results[location]['变量重要性'] = importance_dict
                    regression_results[location]['截距'] = model.intercept_

                else:
                    print(f"  数据量不足（{len(regression_data)}行），无法进行回归分析")
                    regression_results[location]['错误'] = f"数据量不足（{len(regression_data)}行）"
            else:
                print(f"  可用变量不足（{len(available_features)}个），无法进行回归分析")
                regression_results[location]['错误'] = f"可用变量不足（{len(available_features)}个）"

        return regression_results

    def random_forest_analysis(self):
        """随机森林特征重要性分析"""
        print("\n=== 随机森林特征重要性分析 ===")

        fig, axes = plt.subplots(1, 2, figsize=(16, 6))
        fig.suptitle('随机森林特征重要性分析', fontsize=16, fontweight='bold')

        rf_results = {}

        # 特征变量（除了室内温度）
        feature_cols = [col for col in self.specified_vars.keys() if col != '室内温度均值']

        for i, (data, location) in enumerate([(self.data_location1, "地点1"), (self.data_location2, "地点2")]):
            print(f"\n{location}随机森林分析:")
            rf_results[location] = {}

            # 筛选可用特征
            available_features = [col for col in feature_cols if col in data.columns]

            if len(available_features) >= 3 and '室内温度均值' in data.columns:
                # 准备数据
                rf_data = data[available_features + ['室内温度均值']].dropna()

                if len(rf_data) > 100:
                    X = rf_data[available_features]
                    y = rf_data['室内温度均值']

                    # 分割数据
                    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

                    # 训练随机森林
                    rf_model = RandomForestRegressor(n_estimators=100, random_state=42, max_depth=10)
                    rf_model.fit(X_train, y_train)

                    # 预测和评估
                    y_pred = rf_model.predict(X_test)
                    r2 = r2_score(y_test, y_pred)
                    rmse = np.sqrt(mean_squared_error(y_test, y_pred))

                    print(f"  随机森林模型性能:")
                    print(f"    R² 得分: {r2:.4f}")
                    print(f"    RMSE: {rmse:.4f}℃")
                    print(f"    训练数据: {len(X_train)}行")
                    print(f"    测试数据: {len(X_test)}行")

                    # 特征重要性
                    feature_importance = rf_model.feature_importances_
                    feature_importance_norm = feature_importance / np.sum(feature_importance) * 100

                    print(f"  特征重要性排序:")
                    importance_df = pd.DataFrame({
                        'feature': available_features,
                        'importance': feature_importance_norm
                    }).sort_values('importance', ascending=False)

                    importance_dict = {}
                    for _, row in importance_df.iterrows():
                        var_name = self.specified_vars[row['feature']]
                        importance_dict[var_name] = row['importance']
                        print(f"    {var_name}: {row['importance']:.2f}%")

                    # 绘制特征重要性图
                    display_names = [self.specified_vars[f] for f in importance_df['feature']]
                    axes[i].barh(range(len(importance_df)), importance_df['importance'])
                    axes[i].set_yticks(range(len(importance_df)))
                    axes[i].set_yticklabels(display_names)
                    axes[i].set_xlabel('重要性 (%)')
                    axes[i].set_title(f'{location} - 特征重要性')
                    axes[i].grid(True, alpha=0.3)

                    # 添加数值标签
                    for j, v in enumerate(importance_df['importance']):
                        axes[i].text(v + 0.5, j, f'{v:.1f}%', va='center')

                    # 保存结果
                    rf_results[location]['模型性能'] = {
                        'R²得分': r2,
                        'RMSE': rmse,
                        '训练数据量': len(X_train),
                        '测试数据量': len(X_test)
                    }
                    rf_results[location]['特征重要性'] = importance_dict

                else:
                    print(f"  数据量不足（{len(rf_data)}行）")
                    axes[i].text(0.5, 0.5, '数据量不足', ha='center', va='center', transform=axes[i].transAxes)
                    axes[i].set_title(f'{location} - 特征重要性 (数据不足)')
                    rf_results[location]['错误'] = f"数据量不足（{len(rf_data)}行）"
            else:
                print(f"  可用特征不足（{len(available_features)}个）")
                axes[i].text(0.5, 0.5, '特征不足', ha='center', va='center', transform=axes[i].transAxes)
                axes[i].set_title(f'{location} - 特征重要性 (特征不足)')
                rf_results[location]['错误'] = f"可用特征不足（{len(available_features)}个）"

        plt.tight_layout()
        plt.savefig('随机森林特征重要性.png', dpi=300, bbox_inches='tight')
        plt.show()

        return rf_results

    def save_results_to_txt(self, individual_results, correlation_results, regression_results, rf_results):
        """保存分析结果到txt文件"""
        print("\n=== 保存分析结果 ===")

        with open('指定变量影响分析结果.txt', 'w', encoding='utf-8') as f:
            f.write("指定变量对室内温度影响分析结果\n")
            f.write("="*60 + "\n\n")

            f.write("分析变量：\n")
            for var_col, var_name in self.specified_vars.items():
                f.write(f"  - {var_name} ({var_col})\n")
            f.write("\n")

            # 1. 各变量单独影响分析结果
            f.write("一、各变量对室内温度的单独影响分析\n")
            f.write("-" * 40 + "\n")
            for location, results in individual_results.items():
                f.write(f"\n{location}:\n")
                for var_name, stats in results.items():
                    f.write(f"  {var_name}:\n")
                    f.write(f"    相关系数: {stats['相关系数']}\n")
                    f.write(f"    数据点数: {stats['数据点数']}\n")
                    f.write(f"    变量范围: {stats['变量范围']}\n")
                    f.write(f"    影响程度: {stats['影响程度']}\n")

            # 2. 相关性分析结果
            f.write("\n\n二、变量相关性分析\n")
            f.write("-" * 40 + "\n")
            for location, results in correlation_results.items():
                f.write(f"\n{location}:\n")
                if '与室内温度的相关性' in results:
                    f.write("  与室内温度的相关性:\n")
                    sorted_corr = sorted(results['与室内温度的相关性'].items(),
                                       key=lambda x: abs(x[1]), reverse=True)
                    for var_name, corr in sorted_corr:
                        f.write(f"    {var_name}: {corr:.3f}\n")

                if '变量间强相关性' in results and results['变量间强相关性']:
                    f.write("  变量间强相关性 (|r| > 0.5):\n")
                    for pair, corr in results['变量间强相关性'].items():
                        f.write(f"    {pair}: {corr:.3f}\n")

            # 3. 多元回归分析结果
            f.write("\n\n三、多元回归分析\n")
            f.write("-" * 40 + "\n")
            for location, results in regression_results.items():
                f.write(f"\n{location}:\n")
                if '错误' in results:
                    f.write(f"  错误: {results['错误']}\n")
                else:
                    f.write("  模型性能:\n")
                    perf = results['模型性能']
                    f.write(f"    R² 得分: {perf['R²得分']:.4f}\n")
                    f.write(f"    RMSE: {perf['RMSE']:.4f}℃\n")
                    f.write(f"    数据点数: {perf['数据点数']}\n")

                    f.write("  变量影响系数:\n")
                    for var_name, coef in results['变量系数'].items():
                        f.write(f"    {var_name}: {coef:.4f}\n")

                    f.write("  变量重要性排序:\n")
                    sorted_importance = sorted(results['变量重要性'].items(),
                                             key=lambda x: x[1], reverse=True)
                    for var_name, importance in sorted_importance:
                        f.write(f"    {var_name}: {importance:.2f}%\n")

            # 4. 随机森林分析结果
            f.write("\n\n四、随机森林特征重要性分析\n")
            f.write("-" * 40 + "\n")
            for location, results in rf_results.items():
                f.write(f"\n{location}:\n")
                if '错误' in results:
                    f.write(f"  错误: {results['错误']}\n")
                else:
                    f.write("  模型性能:\n")
                    perf = results['模型性能']
                    f.write(f"    R² 得分: {perf['R²得分']:.4f}\n")
                    f.write(f"    RMSE: {perf['RMSE']:.4f}℃\n")
                    f.write(f"    训练数据量: {perf['训练数据量']}\n")
                    f.write(f"    测试数据量: {perf['测试数据量']}\n")

                    f.write("  特征重要性排序:\n")
                    sorted_importance = sorted(results['特征重要性'].items(),
                                             key=lambda x: x[1], reverse=True)
                    for var_name, importance in sorted_importance:
                        f.write(f"    {var_name}: {importance:.2f}%\n")

            # 5. 总结和建议
            f.write("\n\n五、分析总结与建议\n")
            f.write("-" * 40 + "\n")
            f.write("1. 主要影响因素识别:\n")
            f.write("   基于相关性分析和机器学习模型，识别对室内温度影响最大的变量\n\n")

            f.write("2. 模型性能对比:\n")
            f.write("   - 多元线性回归：解释变量间线性关系\n")
            f.write("   - 随机森林：捕捉非线性关系和变量交互效应\n\n")

            f.write("3. 控制策略建议:\n")
            f.write("   - 重点关注影响程度最大的变量\n")
            f.write("   - 考虑变量间的相关性，避免过度调节\n")
            f.write("   - 利用预测模型优化控制参数\n\n")

            f.write("4. 数据质量评估:\n")
            f.write("   - 检查数据完整性和异常值\n")
            f.write("   - 评估不同地点数据的差异性\n")
            f.write("   - 建议增加数据采集频率和精度\n")

        print("分析结果已保存到 '指定变量影响分析结果.txt'")

    def run_complete_analysis(self):
        """运行完整的指定变量影响分析"""
        print("开始指定变量对室内温度影响分析...")
        print(f"分析变量: {list(self.specified_vars.values())}")

        # 1. 加载和预处理数据
        if not self.load_data():
            return
        self.preprocess_data()

        # 2. 各变量单独影响分析
        individual_results = self.analyze_individual_variables()

        # 3. 相关性矩阵分析
        correlation_results = self.correlation_matrix_analysis()

        # 4. 多元回归分析
        regression_results = self.multiple_regression_analysis()

        # 5. 随机森林分析
        rf_results = self.random_forest_analysis()

        # 6. 保存结果
        self.save_results_to_txt(individual_results, correlation_results, regression_results, rf_results)

        print("\n指定变量影响分析完成！")
        print("生成的文件:")
        print("  - 各变量单独影响分析.png")
        print("  - 指定变量相关性矩阵.png")
        print("  - 随机森林特征重要性.png")
        print("  - 指定变量影响分析结果.txt")

if __name__ == "__main__":
    analyzer = SpecifiedVariablesAnalyzer()
    analyzer.run_complete_analysis()
