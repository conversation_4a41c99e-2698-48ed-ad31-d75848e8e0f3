#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
空气源热泵数据分析 - 问题一
分析室内温度波动规律、室内外温度相关性、热泵能耗与温差关系以及影响因素
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class HeatPumpAnalyzer:
    def __init__(self):
        self.data_location1 = None
        self.data_location2 = None

    def load_data(self):
        """加载数据"""
        print("正在加载数据...")
        try:
            # 读取地点1数据
            self.data_location1 = pd.read_csv('data/附件2/地点1/地点1_完整合并表.csv', encoding='utf-8')
            print(f"地点1数据加载成功，共{len(self.data_location1)}行")

            # 读取地点2数据
            self.data_location2 = pd.read_csv('data/附件2/地点2/地点2_完整合并表.csv', encoding='utf-8')
            print(f"地点2数据加载成功，共{len(self.data_location2)}行")

        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
        return True

    def preprocess_data(self):
        """数据预处理"""
        print("正在进行数据预处理...")

        for i, (data, location) in enumerate([(self.data_location1, "地点1"), (self.data_location2, "地点2")]):
            print(f"\n处理{location}数据:")

            # 转换时间列
            data['时间'] = pd.to_datetime(data['时间'])

            # 提取关键列 - 使用正确的列名
            key_columns = ['时间', '环境温度(℃)', '室内温度均值', '热泵功率(kw)', '设定温度(℃)']

            # 检查列是否存在
            existing_columns = [col for col in key_columns if col in data.columns]
            print(f"可用列: {existing_columns}")

            # 数据清洗
            # 处理异常值和缺失值
            if '室内温度均值' in data.columns:
                # 移除明显异常的温度值
                data = data[(data['室内温度均值'] >= 10) & (data['室内温度均值'] <= 35)]

            if '环境温度(℃)' in data.columns:
                data = data[(data['环境温度(℃)'] >= -20) & (data['环境温度(℃)'] <= 40)]

            # 添加时间特征
            data['小时'] = data['时间'].dt.hour
            data['日期'] = data['时间'].dt.date
            data['月份'] = data['时间'].dt.month
            data['星期'] = data['时间'].dt.dayofweek

            # 计算温差
            if '室内温度均值' in data.columns and '环境温度(℃)' in data.columns:
                data['室内外温差'] = data['室内温度均值'] - data['环境温度(℃)']

            # 更新数据
            if i == 0:
                self.data_location1 = data
            else:
                self.data_location2 = data

            print(f"{location}预处理完成，剩余{len(data)}行数据")

    def analyze_temperature_fluctuation(self):
        """分析室内温度波动规律"""
        print("\n=== 室内温度波动规律分析 ===")

        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('室内温度波动规律分析', fontsize=16, fontweight='bold')

        for i, (data, location) in enumerate([(self.data_location1, "地点1"), (self.data_location2, "地点2")]):
            if '室内温度均值' not in data.columns:
                continue

            # 1. 日内温度变化规律
            hourly_temp = data.groupby('小时')['室内温度均值'].agg(['mean', 'std', 'min', 'max'])

            axes[i, 0].plot(hourly_temp.index, hourly_temp['mean'], 'o-', linewidth=2, markersize=4)
            axes[i, 0].fill_between(hourly_temp.index,
                                   hourly_temp['mean'] - hourly_temp['std'],
                                   hourly_temp['mean'] + hourly_temp['std'],
                                   alpha=0.3)
            axes[i, 0].set_title(f'{location} - 日内温度变化')
            axes[i, 0].set_xlabel('小时')
            axes[i, 0].set_ylabel('室内温度 (℃)')
            axes[i, 0].grid(True, alpha=0.3)

            # 2. 温度分布直方图
            axes[i, 1].hist(data['室内温度均值'].dropna(), bins=30, alpha=0.7, density=True)
            axes[i, 1].axvline(data['室内温度均值'].mean(), color='red', linestyle='--',
                              label=f'均值: {data["室内温度均值"].mean():.1f}℃')
            axes[i, 1].axvline(20, color='green', linestyle='--', alpha=0.7, label='目标温度: 20℃')
            axes[i, 1].set_title(f'{location} - 温度分布')
            axes[i, 1].set_xlabel('室内温度 (℃)')
            axes[i, 1].set_ylabel('密度')
            axes[i, 1].legend()
            axes[i, 1].grid(True, alpha=0.3)

            # 3. 时间序列趋势
            sample_data = data.iloc[::24]  # 每天采样一次
            axes[i, 2].plot(sample_data['时间'], sample_data['室内温度均值'], alpha=0.7)
            axes[i, 2].set_title(f'{location} - 温度时间序列')
            axes[i, 2].set_xlabel('时间')
            axes[i, 2].set_ylabel('室内温度 (℃)')
            axes[i, 2].tick_params(axis='x', rotation=45)
            axes[i, 2].grid(True, alpha=0.3)

            # 统计信息
            temp_stats = data['室内温度均值'].describe()
            print(f"\n{location}室内温度统计:")
            print(f"  均值: {temp_stats['mean']:.2f}℃")
            print(f"  标准差: {temp_stats['std']:.2f}℃")
            print(f"  最小值: {temp_stats['min']:.2f}℃")
            print(f"  最大值: {temp_stats['max']:.2f}℃")
            print(f"  温度范围: {temp_stats['max'] - temp_stats['min']:.2f}℃")

            # 舒适度分析
            comfort_range = (data['室内温度均值'] >= 19) & (data['室内温度均值'] <= 21)
            comfort_rate = comfort_range.sum() / len(data) * 100
            print(f"  舒适度(19-21℃)达标率: {comfort_rate:.1f}%")

        plt.tight_layout()
        plt.savefig('室内温度波动规律分析.png', dpi=300, bbox_inches='tight')
        plt.show()

    def analyze_indoor_outdoor_correlation(self):
        """分析室内外温度相关性"""
        print("\n=== 室内外温度相关性分析 ===")

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('室内外温度相关性分析', fontsize=16, fontweight='bold')

        for i, (data, location) in enumerate([(self.data_location1, "地点1"), (self.data_location2, "地点2")]):
            if '室内温度均值' not in data.columns or '环境温度(℃)' not in data.columns:
                continue

            # 清理数据
            clean_data = data[['室内温度均值', '环境温度(℃)']].dropna()

            if len(clean_data) == 0:
                continue

            # 1. 散点图和回归线
            axes[i, 0].scatter(clean_data['环境温度(℃)'], clean_data['室内温度均值'],
                              alpha=0.5, s=10)

            # 计算回归线
            z = np.polyfit(clean_data['环境温度(℃)'], clean_data['室内温度均值'], 1)
            p = np.poly1d(z)
            x_range = np.linspace(clean_data['环境温度(℃)'].min(), clean_data['环境温度(℃)'].max(), 100)
            axes[i, 0].plot(x_range, p(x_range), "r--", alpha=0.8, linewidth=2)

            # 计算相关系数
            correlation = clean_data['环境温度(℃)'].corr(clean_data['室内温度均值'])

            axes[i, 0].set_title(f'{location} - 相关性 (r={correlation:.3f})')
            axes[i, 0].set_xlabel('环境温度 (℃)')
            axes[i, 0].set_ylabel('室内温度 (℃)')
            axes[i, 0].grid(True, alpha=0.3)

            # 2. 温差时间序列
            if '室内外温差' in data.columns:
                sample_data = data.iloc[::24]  # 每天采样一次
                axes[i, 1].plot(sample_data['时间'], sample_data['室内外温差'], alpha=0.7)
                axes[i, 1].axhline(y=0, color='red', linestyle='--', alpha=0.5)
                axes[i, 1].set_title(f'{location} - 室内外温差变化')
                axes[i, 1].set_xlabel('时间')
                axes[i, 1].set_ylabel('温差 (℃)')
                axes[i, 1].tick_params(axis='x', rotation=45)
                axes[i, 1].grid(True, alpha=0.3)

            print(f"\n{location}相关性分析:")
            print(f"  相关系数: {correlation:.3f}")

            if abs(correlation) > 0.7:
                print("  相关性: 强相关")
            elif abs(correlation) > 0.3:
                print("  相关性: 中等相关")
            else:
                print("  相关性: 弱相关")

        plt.tight_layout()
        plt.savefig('室内外温度相关性分析.png', dpi=300, bbox_inches='tight')
        plt.show()

    def analyze_energy_temperature_relationship(self):
        """分析热泵能耗与温差的定量关系"""
        print("\n=== 热泵能耗与温差关系分析 ===")

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('热泵能耗与温差关系分析', fontsize=16, fontweight='bold')

        for i, (data, location) in enumerate([(self.data_location1, "地点1"), (self.data_location2, "地点2")]):
            if '热泵功率(kw)' not in data.columns or '室内外温差' not in data.columns:
                continue

            # 清理数据
            clean_data = data[['热泵功率(kw)', '室内外温差', '环境温度(℃)']].dropna()
            clean_data = clean_data[clean_data['热泵功率(kw)'] >= 0]  # 移除负功率值

            if len(clean_data) == 0:
                continue

            # 1. 功率与温差散点图
            axes[i, 0].scatter(clean_data['室内外温差'], clean_data['热泵功率(kw)'],
                              alpha=0.6, s=15)

            # 拟合回归线
            if len(clean_data) > 10:
                z = np.polyfit(clean_data['室内外温差'], clean_data['热泵功率(kw)'], 1)
                p = np.poly1d(z)
                x_range = np.linspace(clean_data['室内外温差'].min(),
                                    clean_data['室内外温差'].max(), 100)
                axes[i, 0].plot(x_range, p(x_range), "r--", alpha=0.8, linewidth=2)

                # 计算相关系数
                correlation = clean_data['室内外温差'].corr(clean_data['热泵功率(kw)'])
                axes[i, 0].set_title(f'{location} - 功率vs温差 (r={correlation:.3f})')
            else:
                axes[i, 0].set_title(f'{location} - 功率vs温差')

            axes[i, 0].set_xlabel('室内外温差 (℃)')
            axes[i, 0].set_ylabel('热泵功率 (kW)')
            axes[i, 0].grid(True, alpha=0.3)

            # 2. 功率与环境温度关系
            axes[i, 1].scatter(clean_data['环境温度(℃)'], clean_data['热泵功率(kw)'],
                              alpha=0.6, s=15, color='orange')

            if len(clean_data) > 10:
                z2 = np.polyfit(clean_data['环境温度(℃)'], clean_data['热泵功率(kw)'], 1)
                p2 = np.poly1d(z2)
                x_range2 = np.linspace(clean_data['环境温度(℃)'].min(),
                                     clean_data['环境温度(℃)'].max(), 100)
                axes[i, 1].plot(x_range2, p2(x_range2), "r--", alpha=0.8, linewidth=2)

                correlation2 = clean_data['环境温度(℃)'].corr(clean_data['热泵功率(kw)'])
                axes[i, 1].set_title(f'{location} - 功率vs环境温度 (r={correlation2:.3f})')
            else:
                axes[i, 1].set_title(f'{location} - 功率vs环境温度')

            axes[i, 1].set_xlabel('环境温度 (℃)')
            axes[i, 1].set_ylabel('热泵功率 (kW)')
            axes[i, 1].grid(True, alpha=0.3)

            # 统计分析
            power_stats = clean_data['热泵功率(kw)'].describe()
            print(f"\n{location}热泵功率统计:")
            print(f"  平均功率: {power_stats['mean']:.2f} kW")
            print(f"  最大功率: {power_stats['max']:.2f} kW")
            print(f"  功率标准差: {power_stats['std']:.2f} kW")

            if len(clean_data) > 10:
                print(f"  功率与温差相关性: {correlation:.3f}")
                print(f"  功率与环境温度相关性: {correlation2:.3f}")

        plt.tight_layout()
        plt.savefig('热泵能耗与温差关系分析.png', dpi=300, bbox_inches='tight')
        plt.show()

    def analyze_influencing_factors(self):
        """分析影响室内温度的因素"""
        print("\n=== 影响室内温度因素分析 ===")

        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('影响室内温度的因素分析', fontsize=16, fontweight='bold')

        for i, (data, location) in enumerate([(self.data_location1, "地点1"), (self.data_location2, "地点2")]):
            if '室内温度均值' not in data.columns:
                continue

            # 1. 时间因素 - 小时
            hourly_temp = data.groupby('小时')['室内温度均值'].mean()
            axes[i, 0].bar(hourly_temp.index, hourly_temp.values, alpha=0.7)
            axes[i, 0].set_title(f'{location} - 小时影响')
            axes[i, 0].set_xlabel('小时')
            axes[i, 0].set_ylabel('平均室内温度 (℃)')
            axes[i, 0].grid(True, alpha=0.3)

            # 2. 设定温度影响
            if '设定温度(℃)' in data.columns:
                clean_data = data[['室内温度均值', '设定温度(℃)']].dropna()
                if len(clean_data) > 0:
                    axes[i, 1].scatter(clean_data['设定温度(℃)'], clean_data['室内温度均值'],
                                      alpha=0.6, s=15)

                    # 理想线 (y=x)
                    min_temp = min(clean_data['设定温度(℃)'].min(), clean_data['室内温度均值'].min())
                    max_temp = max(clean_data['设定温度(℃)'].max(), clean_data['室内温度均值'].max())
                    axes[i, 1].plot([min_temp, max_temp], [min_temp, max_temp],
                                   'r--', alpha=0.8, label='理想线')

                    correlation = clean_data['设定温度(℃)'].corr(clean_data['室内温度均值'])
                    axes[i, 1].set_title(f'{location} - 设定温度影响 (r={correlation:.3f})')
                    axes[i, 1].legend()
                else:
                    axes[i, 1].set_title(f'{location} - 设定温度影响 (无数据)')
            else:
                axes[i, 1].set_title(f'{location} - 设定温度影响 (无数据)')

            axes[i, 1].set_xlabel('设定温度 (℃)')
            axes[i, 1].set_ylabel('实际室内温度 (℃)')
            axes[i, 1].grid(True, alpha=0.3)

            # 3. 环境温度影响
            if '环境温度(℃)' in data.columns:
                clean_data = data[['室内温度均值', '环境温度(℃)']].dropna()
                if len(clean_data) > 0:
                    # 按环境温度分组分析
                    temp_bins = pd.cut(clean_data['环境温度(℃)'], bins=10)
                    temp_grouped = clean_data.groupby(temp_bins)['室内温度均值'].mean()

                    bin_centers = [interval.mid for interval in temp_grouped.index]
                    axes[i, 2].plot(bin_centers, temp_grouped.values, 'o-', linewidth=2)
                    axes[i, 2].set_title(f'{location} - 环境温度影响')
                else:
                    axes[i, 2].set_title(f'{location} - 环境温度影响 (无数据)')
            else:
                axes[i, 2].set_title(f'{location} - 环境温度影响 (无数据)')

            axes[i, 2].set_xlabel('环境温度 (℃)')
            axes[i, 2].set_ylabel('平均室内温度 (℃)')
            axes[i, 2].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('影响因素分析.png', dpi=300, bbox_inches='tight')
        plt.show()

    def generate_summary_report(self):
        """生成分析总结报告"""
        print("\n" + "="*60)
        print("                  分析总结报告")
        print("="*60)

        for i, (data, location) in enumerate([(self.data_location1, "地点1"), (self.data_location2, "地点2")]):
            print(f"\n【{location}分析结果】")
            print("-" * 30)

            if '室内温度均值' in data.columns:
                temp_stats = data['室内温度均值'].describe()
                print(f"1. 室内温度特征:")
                print(f"   - 平均温度: {temp_stats['mean']:.2f}℃")
                print(f"   - 温度波动范围: {temp_stats['max'] - temp_stats['min']:.2f}℃")
                print(f"   - 温度标准差: {temp_stats['std']:.2f}℃")

                # 舒适度分析
                comfort_range = (data['室内温度均值'] >= 19) & (data['室内温度均值'] <= 21)
                comfort_rate = comfort_range.sum() / len(data) * 100
                print(f"   - 舒适度达标率: {comfort_rate:.1f}%")

            if '室内温度均值' in data.columns and '环境温度(℃)' in data.columns:
                clean_data = data[['室内温度均值', '环境温度(℃)']].dropna()
                if len(clean_data) > 0:
                    correlation = clean_data['环境温度(℃)'].corr(clean_data['室内温度均值'])
                    print(f"\n2. 室内外温度相关性:")
                    print(f"   - 相关系数: {correlation:.3f}")

                    if abs(correlation) > 0.7:
                        strength = "强"
                    elif abs(correlation) > 0.3:
                        strength = "中等"
                    else:
                        strength = "弱"
                    print(f"   - 相关强度: {strength}相关")

            if '热泵功率(kw)' in data.columns:
                power_data = data['热泵功率(kw)'].dropna()
                power_data = power_data[power_data >= 0]
                if len(power_data) > 0:
                    print(f"\n3. 热泵能耗特征:")
                    print(f"   - 平均功率: {power_data.mean():.2f} kW")
                    print(f"   - 最大功率: {power_data.max():.2f} kW")
                    print(f"   - 功率变异系数: {power_data.std()/power_data.mean()*100:.1f}%")

        print("\n【建议与结论】")
        print("-" * 30)
        print("1. 建筑保温性能:")
        print("   - 分析室内温度波动幅度，评估建筑热惰性")
        print("   - 温度波动小的建筑具有更好的保温性能")

        print("\n2. 系统控制优化:")
        print("   - 根据室内外温度相关性调整控制策略")
        print("   - 强相关性表明需要更精确的环境温度补偿")

        print("\n3. 能耗优化方向:")
        print("   - 分析功率与温差关系，优化供回水温度设定")
        print("   - 利用建筑热惰性，在低电价时段预热")

    def run_analysis(self):
        """运行完整分析流程"""
        print("开始空气源热泵数据分析...")

        # 1. 加载数据
        if not self.load_data():
            return

        # 2. 数据预处理
        self.preprocess_data()

        # 3. 室内温度波动规律分析
        self.analyze_temperature_fluctuation()

        # 4. 室内外温度相关性分析
        self.analyze_indoor_outdoor_correlation()

        # 5. 热泵能耗与温差关系分析
        self.analyze_energy_temperature_relationship()

        # 6. 影响因素分析
        self.analyze_influencing_factors()

        # 7. 生成总结报告
        self.generate_summary_report()

        print("\n分析完成！所有图表已保存。")

if __name__ == "__main__":
    analyzer = HeatPumpAnalyzer()
    analyzer.run_analysis()
