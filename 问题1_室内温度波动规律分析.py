#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
空气源热泵室内温度波动规律分析
问题1：统计所给不同建筑的室内温度波动规律；绘制室内外温度相关性曲线，
分析热泵能耗与温差的定量关系；分析影响室内温度的影响因素。

包含4个子问题：
1.1 时间序列分解图，温度分布图，温度时序图，温度统计
1.2 温度相关性散点图
1.3 温差区间能耗分布，能耗温差分析，能耗温差散点图
1.4 变量相关性热力图
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.linear_model import LinearRegression
from sklearn.metrics import r2_score
import warnings
warnings.filterwarnings('ignore')

# 直接设置中文字体和数学符号
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['mathtext.fontset'] = 'stix'
plt.rcParams['mathtext.default'] = 'regular'

# 设置图形样式
try:
    plt.style.use('seaborn-v0_8')
except:
    try:
        plt.style.use('seaborn')
    except:
        pass  # 使用默认样式

sns.set_palette("husl")

print("字体设置完成")

class TemperatureAnalysis:
    """室内温度波动规律分析类"""

    def __init__(self, data_path1, data_path2):
        """初始化分析类"""
        self.data_path1 = data_path1
        self.data_path2 = data_path2
        self.df1 = None
        self.df2 = None
        self.combined_data = None

    def load_data(self):
        """加载数据"""
        print("正在加载数据...")

        # 加载地点1数据
        self.df1 = pd.read_csv(self.data_path1, encoding='utf-8')
        self.df1['地点'] = '地点1'

        # 加载地点2数据
        self.df2 = pd.read_csv(self.data_path2, encoding='utf-8')
        self.df2['地点'] = '地点2'

        # 数据预处理
        self._preprocess_data()

        print(f"地点1数据形状: {self.df1.shape}")
        print(f"地点2数据形状: {self.df2.shape}")

    def _preprocess_data(self):
        """数据预处理"""
        for df in [self.df1, self.df2]:
            # 转换时间列
            df['时间'] = pd.to_datetime(df['时间'])

            # 打印列名以便调试
            print(f"数据列名: {list(df.columns)}")

            # 处理数值列，将非数值转换为NaN
            numeric_cols = ['室内温度均值', '环境温度(℃)', '供温(℃)', '回温(℃)',
                          '设定温度(℃)', '热泵功率(kw)', '流速(m3h)']

            for col in numeric_cols:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
                    print(f"  处理列: {col} - 有效数据点: {df[col].notna().sum()}")

            # 计算温差
            if '环境温度(℃)' in df.columns and '室内温度均值' in df.columns:
                df['室内外温差'] = df['室内温度均值'] - df['环境温度(℃)']
                print(f"  计算室内外温差 - 有效数据点: {df['室内外温差'].notna().sum()}")

            if '供温(℃)' in df.columns and '回温(℃)' in df.columns:
                df['供回温差'] = df['供温(℃)'] - df['回温(℃)']
                print(f"  计算供回温差 - 有效数据点: {df['供回温差'].notna().sum()}")

        # 合并数据
        self.combined_data = pd.concat([self.df1, self.df2], ignore_index=True)

    def create_output_structure(self):
        """创建输出文件夹结构"""
        import os

        # 主文件夹
        main_folder = "问题1_室内温度波动规律分析"
        if not os.path.exists(main_folder):
            os.makedirs(main_folder)

        # 子问题文件夹
        subfolders = [
            "1.1_时间序列分解",
            "1.2_温度相关性分析",
            "1.3_能耗温差分析",
            "1.4_变量相关性分析"
        ]

        for subfolder in subfolders:
            path = os.path.join(main_folder, subfolder)
            if not os.path.exists(path):
                os.makedirs(path)

        return main_folder

    def analyze_time_series(self, output_dir):
        """1.1 时间序列分解分析"""
        print("\n=== 1.1 时间序列分解分析 ===")

        subfolder = os.path.join(output_dir, "1.1_时间序列分解")

        # 对每个地点进行分析
        for location, df in [('地点1', self.df1), ('地点2', self.df2)]:
            if df['室内温度均值'].isna().all():
                continue

            # 设置时间为索引
            df_temp = df.set_index('时间').copy()
            df_temp = df_temp.dropna(subset=['室内温度均值'])

            if len(df_temp) < 10:  # 数据太少跳过
                continue

            # 重采样为日均值
            daily_temp = df_temp['室内温度均值'].resample('D').mean()
            daily_temp = daily_temp.dropna()

            if len(daily_temp) < 7:  # 少于一周数据跳过
                continue

            # 1. 时间序列分解图
            self._plot_time_series_decomposition(daily_temp, location, subfolder)

            # 2. 温度分布图
            self._plot_temperature_distribution(df['室内温度均值'].dropna(), location, subfolder)

            # 3. 温度时序图
            self._plot_temperature_timeseries(df, location, subfolder)

        # 4. 温度统计分析
        self._generate_temperature_statistics(output_dir)

    def _plot_time_series_decomposition(self, series, location, output_dir):
        """绘制时间序列分解图"""
        from statsmodels.tsa.seasonal import seasonal_decompose

        try:
            # 进行时间序列分解
            decomposition = seasonal_decompose(series, model='additive', period=7)

            # 创建图形
            fig, axes = plt.subplots(4, 1, figsize=(15, 12))

            # 原始数据
            decomposition.observed.plot(ax=axes[0], title=f'{location} - 原始室内温度时间序列')
            axes[0].set_ylabel('温度 (℃)')

            # 趋势
            decomposition.trend.plot(ax=axes[1], title='趋势分量')
            axes[1].set_ylabel('温度 (℃)')

            # 季节性
            decomposition.seasonal.plot(ax=axes[2], title='季节性分量')
            axes[2].set_ylabel('温度 (℃)')

            # 残差
            decomposition.resid.plot(ax=axes[3], title='残差分量')
            axes[3].set_ylabel('温度 (℃)')
            axes[3].set_xlabel('时间')

            plt.tight_layout()
            plt.savefig(f'{output_dir}/{location}_时间序列分解图.png', dpi=300, bbox_inches='tight')
            plt.close()

        except Exception as e:
            print(f"时间序列分解失败 ({location}): {e}")

    def _plot_temperature_distribution(self, temp_data, location, output_dir):
        """绘制温度分布图"""
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))

        # 直方图
        axes[0].hist(temp_data, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0].set_title(f'{location} - 室内温度分布直方图')
        axes[0].set_xlabel('温度 (℃)')
        axes[0].set_ylabel('频次')
        axes[0].grid(True, alpha=0.3)

        # 箱线图
        axes[1].boxplot(temp_data, vert=True)
        axes[1].set_title(f'{location} - 室内温度箱线图')
        axes[1].set_ylabel('温度 (℃)')
        axes[1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f'{output_dir}/{location}_温度分布图.png', dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_temperature_timeseries(self, df, location, output_dir):
        """绘制温度时序图"""
        fig, ax = plt.subplots(figsize=(15, 8))

        # 绘制室内温度
        if not df['室内温度均值'].isna().all():
            ax.plot(df['时间'], df['室内温度均值'], label='室内温度', linewidth=1.5, alpha=0.8)

        # 绘制环境温度
        if '环境温度' in df.columns and not df['环境温度'].isna().all():
            ax.plot(df['时间'], df['环境温度'], label='环境温度', linewidth=1.5, alpha=0.8)

        # 绘制设定温度
        if '设定温度(℃)' in df.columns and not df['设定温度(℃)'].isna().all():
            ax.plot(df['时间'], df['设定温度(℃)'], label='设定温度', linewidth=1.5, alpha=0.8)

        ax.set_title(f'{location} - 温度时序变化图', fontsize=16)
        ax.set_xlabel('时间', fontsize=12)
        ax.set_ylabel('温度 (℃)', fontsize=12)
        ax.legend()
        ax.grid(True, alpha=0.3)

        # 旋转x轴标签
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(f'{output_dir}/{location}_温度时序图.png', dpi=300, bbox_inches='tight')
        plt.close()

    def _generate_temperature_statistics(self, output_dir):
        """生成温度统计分析报告"""
        subfolder = os.path.join(output_dir, "1.1_时间序列分解")

        stats_report = []
        stats_report.append("=" * 60)
        stats_report.append("室内温度统计分析报告")
        stats_report.append("=" * 60)

        for location, df in [('地点1', self.df1), ('地点2', self.df2)]:
            temp_data = df['室内温度均值'].dropna()
            if len(temp_data) == 0:
                continue

            stats_report.append(f"\n{location} 室内温度统计:")
            stats_report.append("-" * 30)
            stats_report.append(f"数据点数: {len(temp_data)}")
            stats_report.append(f"平均温度: {temp_data.mean():.2f}℃")
            stats_report.append(f"标准差: {temp_data.std():.2f}℃")
            stats_report.append(f"最低温度: {temp_data.min():.2f}℃")
            stats_report.append(f"最高温度: {temp_data.max():.2f}℃")
            stats_report.append(f"温度范围: {temp_data.max() - temp_data.min():.2f}℃")
            stats_report.append(f"25%分位数: {temp_data.quantile(0.25):.2f}℃")
            stats_report.append(f"50%分位数(中位数): {temp_data.quantile(0.5):.2f}℃")
            stats_report.append(f"75%分位数: {temp_data.quantile(0.75):.2f}℃")

            # 计算变异系数
            cv = (temp_data.std() / temp_data.mean()) * 100
            stats_report.append(f"变异系数: {cv:.2f}%")

            # 舒适度分析（20±1℃）
            comfort_range = temp_data[(temp_data >= 19) & (temp_data <= 21)]
            comfort_ratio = len(comfort_range) / len(temp_data) * 100
            stats_report.append(f"舒适温度范围(19-21℃)占比: {comfort_ratio:.1f}%")

        # 保存统计报告
        with open(f'{subfolder}/温度统计.txt', 'w', encoding='utf-8') as f:
            f.write('\n'.join(stats_report))

    def analyze_temperature_correlation(self, output_dir):
        """1.2 温度相关性分析"""
        print("\n=== 1.2 温度相关性分析 ===")

        subfolder = os.path.join(output_dir, "1.2_温度相关性分析")

        for location, df in [('地点1', self.df1), ('地点2', self.df2)]:
            print(f"  处理{location}...")
            # 检查数据可用性
            if '环境温度(℃)' not in df.columns:
                print(f"    {location}: 缺少环境温度列")
                continue
            if df['环境温度(℃)'].isna().all():
                print(f"    {location}: 环境温度数据全为空")
                continue
            if df['室内温度均值'].isna().all():
                print(f"    {location}: 室内温度数据全为空")
                continue

            # 获取有效数据
            valid_data = df[['室内温度均值', '环境温度(℃)']].dropna()
            if len(valid_data) < 10:
                continue

            indoor_temp = valid_data['室内温度均值']
            outdoor_temp = valid_data['环境温度(℃)']

            # 计算相关系数
            correlation = np.corrcoef(indoor_temp, outdoor_temp)[0, 1]

            # 线性回归
            X = outdoor_temp.values.reshape(-1, 1)
            y = indoor_temp.values
            model = LinearRegression()
            model.fit(X, y)
            y_pred = model.predict(X)
            r2 = r2_score(y, y_pred)

            # 绘制散点图
            fig, ax = plt.subplots(figsize=(10, 8))

            # 散点图
            ax.scatter(outdoor_temp, indoor_temp, alpha=0.6, s=30, color='blue', label='数据点')

            # 回归线
            ax.plot(outdoor_temp, y_pred, color='red', linewidth=2, label=f'回归线 (R²={r2:.3f})')

            # 添加统计信息
            ax.text(0.05, 0.95, f'相关系数: {correlation:.3f}\n回归方程: y = {model.coef_[0]:.3f}x + {model.intercept_:.3f}',
                   transform=ax.transAxes, verticalalignment='top',
                   bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

            ax.set_xlabel('环境温度 (℃)', fontsize=12)
            ax.set_ylabel('室内温度 (℃)', fontsize=12)
            ax.set_title(f'{location} - 室内外温度相关性散点图', fontsize=14)
            ax.legend()
            ax.grid(True, alpha=0.3)

            plt.tight_layout()
            plt.savefig(f'{subfolder}/{location}_温度相关性散点图.png', dpi=300, bbox_inches='tight')
            plt.close()

    def analyze_energy_temperature_diff(self, output_dir):
        """1.3 能耗温差分析"""
        print("\n=== 1.3 能耗温差分析 ===")

        subfolder = os.path.join(output_dir, "1.3_能耗温差分析")

        analysis_report = []
        analysis_report.append("=" * 60)
        analysis_report.append("热泵能耗与温差定量关系分析报告")
        analysis_report.append("=" * 60)

        for location, df in [('地点1', self.df1), ('地点2', self.df2)]:
            print(f"  处理{location}...")
            # 检查数据可用性
            if '热泵功率(kw)' not in df.columns:
                print(f"    {location}: 缺少热泵功率列")
                continue
            if df['热泵功率(kw)'].isna().all():
                print(f"    {location}: 热泵功率数据全为空")
                continue
            if '室内外温差' not in df.columns:
                print(f"    {location}: 缺少室内外温差列")
                continue
            if df['室内外温差'].isna().all():
                print(f"    {location}: 室内外温差数据全为空")
                continue

            # 获取有效数据
            valid_data = df[['热泵功率(kw)', '室内外温差', '环境温度(℃)', '室内温度均值']].dropna()
            if len(valid_data) < 10:
                continue

            power = valid_data['热泵功率(kw)']
            temp_diff = valid_data['室内外温差']

            # 1. 能耗温差散点图
            self._plot_energy_temp_scatter(power, temp_diff, location, subfolder)

            # 2. 温差区间能耗分布
            self._plot_temp_diff_energy_distribution(power, temp_diff, location, subfolder)

            # 3. 统计分析
            analysis_report.append(f"\n{location} 能耗温差分析:")
            analysis_report.append("-" * 30)

            # 相关性分析
            correlation = np.corrcoef(power, temp_diff)[0, 1]
            analysis_report.append(f"热泵功率与室内外温差相关系数: {correlation:.3f}")

            # 线性回归
            X = temp_diff.values.reshape(-1, 1)
            y = power.values
            model = LinearRegression()
            model.fit(X, y)
            r2 = r2_score(y, model.predict(X))
            analysis_report.append(f"线性回归R²: {r2:.3f}")
            analysis_report.append(f"回归方程: 功率 = {model.coef_[0]:.3f} × 温差 + {model.intercept_:.3f}")

            # 温差区间分析
            temp_diff_ranges = [(-10, 0), (0, 5), (5, 10), (10, 15), (15, float('inf'))]
            for i, (low, high) in enumerate(temp_diff_ranges):
                if high == float('inf'):
                    mask = temp_diff >= low
                    range_name = f"≥{low}℃"
                else:
                    mask = (temp_diff >= low) & (temp_diff < high)
                    range_name = f"{low}~{high}℃"

                range_data = power[mask]
                if len(range_data) > 0:
                    analysis_report.append(f"温差{range_name}: 平均功率{range_data.mean():.2f}kW, 数据点{len(range_data)}个")

        # 保存分析报告
        with open(f'{subfolder}/能耗温差分析.txt', 'w', encoding='utf-8') as f:
            f.write('\n'.join(analysis_report))

    def _plot_energy_temp_scatter(self, power, temp_diff, location, output_dir):
        """绘制能耗温差散点图"""
        fig, ax = plt.subplots(figsize=(10, 8))

        # 散点图
        ax.scatter(temp_diff, power, alpha=0.6, s=30, color='green', label='数据点')

        # 线性回归线
        X = temp_diff.values.reshape(-1, 1)
        y = power.values
        model = LinearRegression()
        model.fit(X, y)
        y_pred = model.predict(X)
        r2 = r2_score(y, y_pred)

        # 排序用于绘制平滑的回归线
        sort_idx = np.argsort(temp_diff)
        ax.plot(temp_diff.iloc[sort_idx], y_pred[sort_idx], color='red', linewidth=2,
               label=f'回归线 (R²={r2:.3f})')

        # 计算相关系数
        correlation = np.corrcoef(power, temp_diff)[0, 1]

        # 添加统计信息
        ax.text(0.05, 0.95, f'相关系数: {correlation:.3f}\n回归方程: y = {model.coef_[0]:.3f}x + {model.intercept_:.3f}',
               transform=ax.transAxes, verticalalignment='top',
               bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

        ax.set_xlabel('室内外温差 (℃)', fontsize=12)
        ax.set_ylabel('热泵功率 (kW)', fontsize=12)
        ax.set_title(f'{location} - 能耗温差散点图', fontsize=14)
        ax.legend()
        ax.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f'{output_dir}/{location}_能耗温差散点图.png', dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_temp_diff_energy_distribution(self, power, temp_diff, location, output_dir):
        """绘制温差区间能耗分布图"""
        # 定义温差区间
        bins = [-10, 0, 5, 10, 15, 20, 30]
        labels = ['<0℃', '0-5℃', '5-10℃', '10-15℃', '15-20℃', '≥20℃']

        # 分组统计
        temp_diff_binned = pd.cut(temp_diff, bins=bins, labels=labels, include_lowest=True)
        grouped_data = power.groupby(temp_diff_binned)

        # 计算统计量
        mean_power = grouped_data.mean()
        std_power = grouped_data.std()
        count_data = grouped_data.count()

        # 过滤掉没有数据的区间
        valid_idx = count_data > 0
        mean_power = mean_power[valid_idx]
        std_power = std_power[valid_idx]
        count_data = count_data[valid_idx]

        if len(mean_power) == 0:
            return

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        # 平均功率柱状图
        bars = ax1.bar(range(len(mean_power)), mean_power.values,
                      yerr=std_power.values, capsize=5, alpha=0.7, color='orange')
        ax1.set_xlabel('温差区间', fontsize=12)
        ax1.set_ylabel('平均热泵功率 (kW)', fontsize=12)
        ax1.set_title(f'{location} - 温差区间平均能耗分布', fontsize=14)
        ax1.set_xticks(range(len(mean_power)))
        ax1.set_xticklabels(mean_power.index, rotation=45)
        ax1.grid(True, alpha=0.3)

        # 在柱状图上添加数据点数量
        for i, (bar, count) in enumerate(zip(bars, count_data.values)):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + std_power.iloc[i] + 0.1,
                    f'n={count}', ha='center', va='bottom', fontsize=10)

        # 数据点数量分布
        ax2.bar(range(len(count_data)), count_data.values, alpha=0.7, color='skyblue')
        ax2.set_xlabel('温差区间', fontsize=12)
        ax2.set_ylabel('数据点数量', fontsize=12)
        ax2.set_title(f'{location} - 温差区间数据分布', fontsize=14)
        ax2.set_xticks(range(len(count_data)))
        ax2.set_xticklabels(count_data.index, rotation=45)
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f'{output_dir}/{location}_温差区间能耗分布.png', dpi=300, bbox_inches='tight')
        plt.close()

    def analyze_variable_correlation(self, output_dir):
        """1.4 变量相关性分析"""
        print("\n=== 1.4 变量相关性分析 ===")

        subfolder = os.path.join(output_dir, "1.4_变量相关性分析")

        # 分析变量
        variables = ['室内温度均值', '环境温度(℃)', '供温(℃)', '回温(℃)',
                    '设定温度(℃)', '热泵功率(kw)', '流速(m3h)']

        for location, df in [('地点1', self.df1), ('地点2', self.df2)]:
            # 选择可用的变量
            available_vars = [var for var in variables if var in df.columns]
            if len(available_vars) < 3:
                continue

            # 获取数据
            correlation_data = df[available_vars].copy()

            # 移除全为NaN的列
            correlation_data = correlation_data.dropna(axis=1, how='all')

            if correlation_data.shape[1] < 3:
                continue

            # 计算相关系数矩阵
            corr_matrix = correlation_data.corr()

            # 绘制热力图
            fig, ax = plt.subplots(figsize=(12, 10))

            # 创建热力图
            mask = np.triu(np.ones_like(corr_matrix, dtype=bool))  # 只显示下三角
            sns.heatmap(corr_matrix, mask=mask, annot=True, cmap='RdBu_r', center=0,
                       square=True, fmt='.3f', cbar_kws={"shrink": .8}, ax=ax)

            ax.set_title(f'{location} - 变量相关性热力图', fontsize=16, pad=20)

            plt.tight_layout()
            plt.savefig(f'{subfolder}/{location}_变量相关性热力图.png', dpi=300, bbox_inches='tight')
            plt.close()

            # 保存相关系数矩阵
            corr_matrix.to_csv(f'{subfolder}/{location}_相关系数矩阵.csv', encoding='utf-8')

        # 生成相关性分析报告
        self._generate_correlation_report(subfolder)

    def _generate_correlation_report(self, output_dir):
        """生成相关性分析报告"""
        report = []
        report.append("=" * 60)
        report.append("变量相关性分析报告")
        report.append("=" * 60)
        report.append("\n分析说明:")
        report.append("本报告分析了影响室内温度的关键变量之间的相关性关系")
        report.append("包括：室内温度均值、环境温度、供温、回温、设定温度、热泵功率、流速")
        report.append("\n相关性解读:")
        report.append("- 相关系数接近1：强正相关")
        report.append("- 相关系数接近-1：强负相关")
        report.append("- 相关系数接近0：无线性相关")
        report.append("- |相关系数| > 0.7：强相关")
        report.append("- 0.3 < |相关系数| < 0.7：中等相关")
        report.append("- |相关系数| < 0.3：弱相关")

        # 保存报告
        with open(f'{output_dir}/相关性分析说明.txt', 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))

if __name__ == "__main__":
    # 数据路径
    data_path1 = "data/附件2/地点1/地点1_完整合并表.csv"
    data_path2 = "data/附件2/地点2/地点2_完整合并表.csv"

    # 创建分析实例
    analyzer = TemperatureAnalysis(data_path1, data_path2)

    # 加载数据
    analyzer.load_data()

    # 创建输出文件夹
    output_dir = analyzer.create_output_structure()

    print("开始执行室内温度波动规律分析...")
    print("=" * 50)

    # 执行所有分析
    analyzer.analyze_time_series(output_dir)
    analyzer.analyze_temperature_correlation(output_dir)
    analyzer.analyze_energy_temperature_diff(output_dir)
    analyzer.analyze_variable_correlation(output_dir)

    print("\n" + "=" * 50)
    print("分析完成！")
    print(f"结果已保存到文件夹: {output_dir}")
    print("\n输出文件结构:")
    print("├── 1.1_时间序列分解/")
    print("│   ├── 地点1_时间序列分解图.png")
    print("│   ├── 地点1_温度分布图.png")
    print("│   ├── 地点1_温度时序图.png")
    print("│   ├── 地点2_时间序列分解图.png")
    print("│   ├── 地点2_温度分布图.png")
    print("│   ├── 地点2_温度时序图.png")
    print("│   └── 温度统计.txt")
    print("├── 1.2_温度相关性分析/")
    print("│   ├── 地点1_温度相关性散点图.png")
    print("│   └── 地点2_温度相关性散点图.png")
    print("├── 1.3_能耗温差分析/")
    print("│   ├── 地点1_能耗温差散点图.png")
    print("│   ├── 地点1_温差区间能耗分布.png")
    print("│   ├── 地点2_能耗温差散点图.png")
    print("│   ├── 地点2_温差区间能耗分布.png")
    print("│   └── 能耗温差分析.txt")
    print("└── 1.4_变量相关性分析/")
    print("    ├── 地点1_变量相关性热力图.png")
    print("    ├── 地点1_相关系数矩阵.csv")
    print("    ├── 地点2_变量相关性热力图.png")
    print("    ├── 地点2_相关系数矩阵.csv")
    print("    └── 相关性分析说明.txt")
