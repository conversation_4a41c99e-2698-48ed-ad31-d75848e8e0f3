指定变量对室内温度影响分析结果
============================================================

分析变量：
  - 平均室内温度 (室内温度均值)
  - 环境温度 (环境温度(℃))
  - 供温 (供温(℃))
  - 回温 (回温(℃))
  - 设定温度 (设定温度(℃))
  - 热泵功率 (热泵功率(kw))
  - 流速 (流速(m3h))

一、各变量对室内温度的单独影响分析
----------------------------------------

地点1:
  环境温度:
    相关系数: 0.06772157451940874
    数据点数: 8604
    变量范围: -20.00 - 31.10
    影响程度: 弱影响
  供温:
    相关系数: 0.21527845715498484
    数据点数: 8604
    变量范围: 16.20 - 48.70
    影响程度: 弱影响
  回温:
    相关系数: 0.19668403175687954
    数据点数: 8604
    变量范围: 17.50 - 42.80
    影响程度: 弱影响

地点2:
  环境温度:
    相关系数: -0.13437109098312544
    数据点数: 7989
    变量范围: -15.00 - 30.00
    影响程度: 弱影响
  供温:
    相关系数: -0.018455175136650324
    数据点数: 7989
    变量范围: 14.90 - 87.40
    影响程度: 弱影响
  回温:
    相关系数: 0.08760075172874586
    数据点数: 7989
    变量范围: 18.50 - 146.20
    影响程度: 弱影响


二、变量相关性分析
----------------------------------------

地点1:
  与室内温度的相关性:
    供温: 0.215
    回温: 0.197
    流速: 0.160
    热泵功率: 0.081
    环境温度: 0.068
    设定温度: 0.049
  变量间强相关性 (|r| > 0.5):
    环境温度 vs 供温: -0.523
    环境温度 vs 回温: -0.519
    环境温度 vs 设定温度: -0.602
    供温 vs 环境温度: -0.523
    供温 vs 回温: 0.933
    供温 vs 设定温度: 0.727
    供温 vs 热泵功率: 0.552
    回温 vs 环境温度: -0.519
    回温 vs 供温: 0.933
    回温 vs 设定温度: 0.674
    设定温度 vs 环境温度: -0.602
    设定温度 vs 供温: 0.727
    设定温度 vs 回温: 0.674
    热泵功率 vs 供温: 0.552

地点2:
  与室内温度的相关性:
    热泵功率: -0.288
    环境温度: -0.134
    流速: 0.127
    回温: 0.088
    设定温度: 0.046
    供温: -0.018
  变量间强相关性 (|r| > 0.5):
    环境温度 vs 设定温度: -0.574
    供温 vs 回温: 0.798
    供温 vs 设定温度: 0.694
    供温 vs 热泵功率: 0.524
    回温 vs 供温: 0.798
    回温 vs 设定温度: 0.638
    回温 vs 热泵功率: 0.542
    设定温度 vs 环境温度: -0.574
    设定温度 vs 供温: 0.694
    设定温度 vs 回温: 0.638
    热泵功率 vs 供温: 0.524
    热泵功率 vs 回温: 0.542


三、多元回归分析
----------------------------------------

地点1:
  模型性能:
    R² 得分: 0.1281
    RMSE: 0.8923℃
    数据点数: 8604
  变量影响系数:
    环境温度: 0.2498
    供温: 0.7568
    回温: -0.3390
    设定温度: -0.0745
    热泵功率: -0.1718
    流速: 0.1675
  变量重要性排序:
    供温: 43.01%
    回温: 19.27%
    环境温度: 14.20%
    热泵功率: 9.76%
    流速: 9.52%
    设定温度: 4.24%

地点2:
  模型性能:
    R² 得分: 0.2058
    RMSE: 0.8966℃
    数据点数: 7989
  变量影响系数:
    环境温度: 0.0415
    供温: -0.1832
    回温: 0.4842
    设定温度: -0.0132
    热泵功率: -0.4974
    流速: 0.1900
  变量重要性排序:
    热泵功率: 35.29%
    回温: 34.35%
    流速: 13.48%
    供温: 12.99%
    环境温度: 2.95%
    设定温度: 0.94%


四、随机森林特征重要性分析
----------------------------------------

地点1:
  模型性能:
    R² 得分: 0.5367
    RMSE: 0.6579℃
    训练数据量: 6883
    测试数据量: 1721
  特征重要性排序:
    流速: 46.96%
    环境温度: 14.91%
    回温: 14.73%
    设定温度: 12.23%
    供温: 6.91%
    热泵功率: 4.26%

地点2:
  模型性能:
    R² 得分: 0.4636
    RMSE: 0.7325℃
    训练数据量: 6391
    测试数据量: 1598
  特征重要性排序:
    热泵功率: 40.59%
    流速: 22.62%
    回温: 17.85%
    供温: 7.86%
    环境温度: 7.01%
    设定温度: 4.08%


五、分析总结与建议
----------------------------------------
1. 主要影响因素识别:
   基于相关性分析和机器学习模型，识别对室内温度影响最大的变量

2. 模型性能对比:
   - 多元线性回归：解释变量间线性关系
   - 随机森林：捕捉非线性关系和变量交互效应

3. 控制策略建议:
   - 重点关注影响程度最大的变量
   - 考虑变量间的相关性，避免过度调节
   - 利用预测模型优化控制参数

4. 数据质量评估:
   - 检查数据完整性和异常值
   - 评估不同地点数据的差异性
   - 建议增加数据采集频率和精度
