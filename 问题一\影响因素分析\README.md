# 指定变量对室内温度影响分析

## 分析目标
专门针对用户指定的关键变量进行深度影响因素分析，识别对室内温度影响最大的因素。

## 分析变量
- **平均室内温度（℃）** - 目标变量
- **环境温度（℃）** - 外界环境因素
- **供温（℃）** - 供水温度
- **回温（℃）** - 回水温度  
- **设定温度（℃）** - 控制目标温度
- **热泵功率（kw）** - 设备运行功率
- **流速（m3h）** - 水循环流速

## 文件说明

### 核心分析脚本
- `指定变量影响分析.py` - 完整的分析脚本，包含所有分析方法

### 生成的图表
- `各变量单独影响分析.png` - 各变量与室内温度的散点图和相关性分析
- `指定变量相关性矩阵.png` - 所有变量间的相关性热力图
- `随机森林特征重要性.png` - 基于机器学习的特征重要性排序

### 分析结果文档
- `指定变量影响分析结果.txt` - 详细的数值分析结果
- `深度分析总结.txt` - 深度分析总结和实用建议
- `README.md` - 本说明文档

## 主要发现

### 地点1（建筑1）
**最重要影响因素：**
1. **流速**（随机森林：46.96%）- 非线性影响最大
2. **供温**（线性回归：43.01%）- 线性影响最大
3. **回温**（19.27%）- 系统效率指标

**模型性能：**
- 随机森林：R² = 0.5367, RMSE = 0.6579℃
- 线性回归：R² = 0.1281, RMSE = 0.8923℃

### 地点2（建筑2）
**最重要影响因素：**
1. **热泵功率**（40.59%）- 主导因素
2. **流速**（22.62%）- 重要循环参数
3. **回温**（17.85%）- 系统效率

**模型性能：**
- 随机森林：R² = 0.4636, RMSE = 0.7325℃
- 线性回归：R² = 0.2058, RMSE = 0.8966℃

## 关键洞察

1. **两地点差异显著**
   - 地点1：供温和流速主导
   - 地点2：热泵功率主导
   - 需要分别制定控制策略

2. **非线性关系重要**
   - 随机森林模型性能远超线性回归
   - 变量间存在复杂的交互效应

3. **强相关性变量**
   - 供温与回温高度相关（r>0.8）
   - 环境温度与设定温度负相关
   - 需要避免过度调节

## 实用建议

### 地点1优化策略
- **重点控制流速**：非线性影响最大
- **精确控制供温**：线性影响最大
- **监控回温变化**：系统效率指标

### 地点2优化策略
- **热泵功率管理**：建立精确功率控制算法
- **流速优化**：与功率配合实现最佳效果
- **回温监控**：实时监控系统状态

### 通用建议
- 采用机器学习控制方法
- 考虑变量交互效应
- 建立分地点差异化控制
- 实时数据监控和异常检测

## 运行方法
```bash
cd 问题一/影响因素分析
python 指定变量影响分析.py
```

## 技术特点
- **多种分析方法**：相关性分析、多元回归、随机森林
- **可视化展示**：散点图、热力图、重要性排序图
- **详细结果输出**：数值结果和文字分析并重
- **实用性强**：提供具体的优化建议和实施路径

这些分析结果为空气源热泵系统的优化控制和4小时预测模型的建立提供了重要的科学依据。
