#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
bili_comments_full.py
---------------------
抓取指定 BV 号所有『一级评论』，保存为 comments_<BV号>.csv

依赖：pip install requests
"""

import csv, random, re, time, json, requests
from pathlib import Path

# ════【必改】═══════════════════════════════════════════════
BV          = "BV139SbYPE6u"        # 目标视频 BV 号
SORT        = 2                     # 2=按时间 | 0=按热度
CSV_PATH    = Path(f"comments_{BV}.csv")
COOKIE_FILE = ""                    # 若需登录，把 cookie 字符串存 .txt；否则留空
# ══════════════════════════════════════════════════════════

HEADERS = {
    "User-Agent": (
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
        "AppleWebKit/537.36 (KHTML, like Gecko) "
        "Chrome/124.0 Safari/537.36"
    ),
    "Referer": f"https://www.bilibili.com/video/{BV}",
}

PAGE_SIZE   = 20           # 官方上限
DELAY_RANGE = (0.8, 1.3)   # 随机延迟，避免 412

# ---------- 工具 ----------
def load_cookie_dict(path: str) -> dict:
    if not path or not Path(path).is_file():
        return {}
    raw = Path(path).read_text().strip().rstrip(";")
    return {k.strip(): v.strip() for k, v in
            (kv.split("=", 1) for kv in raw.split(";") if "=" in kv)}

def bv2av(bv: str) -> int:
    """用官方接口把 BV 转 aid"""
    r = requests.get(
        "https://api.bilibili.com/x/web-interface/view",
        params={"bvid": bv},
        headers=HEADERS,
        timeout=10,
    ).json()
    if r["code"] != 0:
        raise RuntimeError(f"BV 转 av 失败：{r['code']} {r['message']}")
    return r["data"]["aid"]

# ---------- 翻页 ----------
def iter_reply_pages(sess: requests.Session, aid: int):
    pn = 1
    while True:
        params = {
            "jsonp": "jsonp",
            "pn": pn,
            "type": 1,
            "oid": aid,
            "sort": SORT,
            "ps": PAGE_SIZE,
        }
        resp = sess.get(
            "https://api.bilibili.com/x/v2/reply",
            headers=HEADERS,
            params=params,
            timeout=10,
        )
        data = resp.json()
        if data["code"] != 0:
            raise RuntimeError(f"接口错误：{data['code']} {data['message']}")
        replies = data["data"]["replies"] or []
        yield replies
        if len(replies) < PAGE_SIZE:
            break      # 最后一页
        pn += 1
        time.sleep(random.uniform(*DELAY_RANGE))

# ---------- 主程序 ----------
def main():
    aid = bv2av(BV)
    print(f"{BV} → av{aid}")

    sess = requests.Session()
    sess.headers.update(HEADERS)
    sess.cookies.update(load_cookie_dict(COOKIE_FILE))

    with CSV_PATH.open("w", newline="", encoding="utf-8-sig") as f:
        wr = csv.DictWriter(
            f, fieldnames=["rpid", "user_name", "user_mid", "ctime", "like", "message"]
        )
        wr.writeheader()

        total = 0
        for page in iter_reply_pages(sess, aid):
            rows = [
                {
                    "rpid": rep["rpid"],
                    "user_name": rep["member"]["uname"],
                    "user_mid": rep["member"]["mid"],
                    "ctime": time.strftime(
                        "%Y-%m-%d %H:%M:%S", time.localtime(rep["ctime"])
                    ),
                    "like": rep["like"],
                    "message": re.sub(r"[\r\n]+", " ", rep["content"]["message"]).strip(),
                }
                for rep in page
            ]
            wr.writerows(rows)
            total += len(rows)
            print(f"抓到 {len(rows):>2} 条，累计 {total}")

    print("✅ 全部完成 →", CSV_PATH.resolve())

if __name__ == "__main__":
    main()
