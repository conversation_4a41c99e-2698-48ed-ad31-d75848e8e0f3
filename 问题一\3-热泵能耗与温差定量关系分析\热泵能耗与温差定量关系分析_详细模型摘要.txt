热泵能耗与温差定量关系分析 - 详细模型摘要报告
======================================================================

一、分析目的
------------------------------
建立热泵功率与温差的定量关系模型，包括：
1. 多种机器学习模型对比（线性回归、多项式回归、随机森林）
2. 详细的模型性能评估和参数分析
3. 特征重要性分析
4. 模型适用性评估和选择建议

二、使用的模型
------------------------------
1. 线性回归 (Linear Regression):
   - 原理: 假设功率与特征变量存在线性关系
   - 优点: 简单、可解释性强、计算快速
   - 缺点: 无法捕捉非线性关系
   - 适用场景: 变量间关系相对简单的情况

2. 多项式回归 (Polynomial Regression):
   - 原理: 通过多项式特征扩展捕捉非线性关系
   - 优点: 能捕捉一定的非线性关系，仍具有较好的可解释性
   - 缺点: 容易过拟合，特征数量增长快
   - 适用场景: 存在明显非线性但不太复杂的关系

3. 随机森林 (Random Forest):
   - 原理: 集成多个决策树，通过投票或平均得到最终结果
   - 优点: 能处理复杂非线性关系，抗过拟合能力强，提供特征重要性
   - 缺点: 可解释性相对较差，计算复杂度高
   - 适用场景: 复杂的非线性关系，特征较多的情况

三、数据处理
------------------------------
1. 数据清洗:
   - 移除负功率值（物理上不合理）
   - 移除缺失值
   - 移除异常值（超过99%分位数的极值）

2. 特征工程:
   - 计算室内外温差
   - 选择相关性较强的特征变量

3. 数据分割:
   - 训练集: 80%
   - 测试集: 20%
   - 随机种子: 42（确保结果可重现）

四、评估指标说明
------------------------------
1. R²（决定系数）:
   - 范围: 0-1，越接近1越好
   - 含义: 模型解释的方差比例
   - 评判标准: >0.7优秀，0.5-0.7良好，0.3-0.5一般，<0.3较差

2. RMSE（均方根误差）:
   - 单位: kW
   - 含义: 预测值与真实值的平均偏差
   - 评判标准: 越小越好，相对于功率均值的比例<10%为优秀

3. MAE（平均绝对误差）:
   - 单位: kW
   - 含义: 预测值与真实值绝对偏差的平均值
   - 评判标准: 越小越好，对异常值不敏感

五、详细分析结果
------------------------------

【地点1分析结果】
数据概况:
  总数据点: 8576
  训练集大小: 6860
  测试集大小: 1716
  特征变量: 室内外温差, 环境温度(℃)

功率统计特征:
  平均功率: 306.70 kW
  功率标准差: 201.87 kW
  功率范围: 0.00 - 811.50 kW
  变异系数: 65.8%

模型性能详细对比:

线性回归:
  R²: 0.0389
  RMSE: 200.74 kW
  MAE: 166.75 kW
  相对RMSE: 65.5%
  回归方程: 功率 = -170.181 + 22.628×室内外温差 + 18.878×环境温度(℃)
  系数解释:
    室内外温差: 22.628 (每增加1单位，功率变化22.628kW)
    环境温度(℃): 18.878 (每增加1单位，功率变化18.878kW)
  截距: -170.181 kW

  详细回归统计表格:

================================================================================
地点1 线性回归 回归摘要
================================================================================
Dep. Variable:           热泵功率(kw)    R-squared:           0.0292
Model:                   OLS             Adj. R-squared:      0.0289
No. Observations:        6860           F-statistic:         102.9944
Df Residuals:            6857           Prob (F-statistic):  1.110e-16
Df Model:                2           
Covariance Type:         nonrobust       
================================================================================
Variable             coef         std err      t        P>|t|    [0.025   0.975]  
--------------------------------------------------------------------------------
const                -170.1812    51.8793      -3.280   0.001    -271.881 -68.482 
室内外温差                22.6275      2.5213       8.975    0.000    17.685   27.570  
环境温度(℃)              18.8777      2.5115       7.516    0.000    13.954   23.801  
================================================================================
Omnibus:                 N/A             Durbin-Watson:       N/A
Prob(Omnibus):           N/A             Jarque-Bera (JB):    N/A
Skew:                    N/A             Prob(JB):            N/A
Kurtosis:                N/A             Cond. No.            N/A
================================================================================


多项式回归:
  R²: 0.0579
  RMSE: 198.75 kW
  MAE: 164.47 kW
  相对RMSE: 64.8%
  多项式次数: 2
  扩展后特征数: 5
  特征名称: 室内外温差, 环境温度(℃), 室内外温差^2, 室内外温差 环境温度(℃), 环境温度(℃)^2

随机森林:
  R²: 0.0672
  RMSE: 197.76 kW
  MAE: 162.44 kW
  相对RMSE: 64.5%
  模型参数:
    树的数量: 100
    最大深度: 10
  特征重要性排序:
    室内外温差: 0.602 (60.2%)
    环境温度(℃): 0.398 (39.8%)

推荐模型: 随机森林
  R²: 0.0672
  RMSE: 197.76 kW
  推荐理由: 在测试集上表现最佳

【地点2分析结果】
数据概况:
  总数据点: 7909
  训练集大小: 6327
  测试集大小: 1582
  特征变量: 室内外温差, 环境温度(℃)

功率统计特征:
  平均功率: 192.25 kW
  功率标准差: 162.34 kW
  功率范围: 0.00 - 707.50 kW
  变异系数: 84.4%

模型性能详细对比:

线性回归:
  R²: 0.1416
  RMSE: 153.34 kW
  MAE: 117.66 kW
  相对RMSE: 79.8%
  回归方程: 功率 = 1253.051 - 54.022×室内外温差 - 52.555×环境温度(℃)
  系数解释:
    室内外温差: -54.022 (每增加1单位，功率变化-54.022kW)
    环境温度(℃): -52.555 (每增加1单位，功率变化-52.555kW)
  截距: 1253.051 kW

  详细回归统计表格:

================================================================================
地点2 线性回归 回归摘要
================================================================================
Dep. Variable:           热泵功率(kw)    R-squared:           0.1231
Model:                   OLS             Adj. R-squared:      0.1228
No. Observations:        6327           F-statistic:         443.9878
Df Residuals:            6324           Prob (F-statistic):  1.110e-16
Df Model:                2           
Covariance Type:         nonrobust       
================================================================================
Variable             coef         std err      t        P>|t|    [0.025   0.975]  
--------------------------------------------------------------------------------
const                1253.0513    37.8187      33.133   0.000    1178.914 1327.189
室内外温差                -54.0225     1.9090       -28.298  0.000    -57.765  -50.280 
环境温度(℃)              -52.5554     1.9729       -26.638  0.000    -56.423  -48.688 
================================================================================
Omnibus:                 N/A             Durbin-Watson:       N/A
Prob(Omnibus):           N/A             Jarque-Bera (JB):    N/A
Skew:                    N/A             Prob(JB):            N/A
Kurtosis:                N/A             Cond. No.            N/A
================================================================================


多项式回归:
  R²: 0.1848
  RMSE: 149.43 kW
  MAE: 111.65 kW
  相对RMSE: 77.7%
  多项式次数: 2
  扩展后特征数: 5
  特征名称: 室内外温差, 环境温度(℃), 室内外温差^2, 室内外温差 环境温度(℃), 环境温度(℃)^2

随机森林:
  R²: 0.5574
  RMSE: 110.11 kW
  MAE: 79.94 kW
  相对RMSE: 57.3%
  模型参数:
    树的数量: 100
    最大深度: 10
  特征重要性排序:
    环境温度(℃): 0.788 (78.8%)
    室内外温差: 0.212 (21.2%)

推荐模型: 随机森林
  R²: 0.5574
  RMSE: 110.11 kW
  推荐理由: 在测试集上表现最佳

六、模型选择建议
------------------------------
1. 模型性能评估:
   - 优先考虑R²值最高的模型
   - 同时关注RMSE和MAE，确保预测精度
   - 避免过拟合，测试集性能应接近训练集

2. 实际应用考虑:
   - 如需要可解释性，优选线性回归
   - 如追求预测精度，可选择随机森林
   - 多项式回归适合中等复杂度的关系

3. 模型部署建议:
   - 定期重新训练模型，适应系统变化
   - 建立模型监控机制，及时发现性能下降
   - 结合领域知识，验证模型预测的合理性

4. 进一步改进方向:
   - 增加更多特征变量（如历史功率、天气数据等）
   - 尝试其他机器学习算法（如XGBoost、神经网络等）
   - 考虑时间序列特征，建立动态预测模型
   - 进行特征选择和降维，提高模型效率
