#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
问题一：统计分析与关系探索
学术化分析框架

包含四个子问题：
1.1 统计所给不同建筑的室内温度波动规律
1.2 绘制室内外温度相关性曲线
1.3 分析热泵能耗与温差的定量关系
1.4 分析影响室内温度的影响因素
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.linear_model import LinearRegression
from sklearn.metrics import r2_score
import statsmodels.api as sm
from scipy.stats import pearsonr
import os
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class AcademicHeatPumpAnalyzer:
    def __init__(self):
        self.data_building1 = None
        self.data_building2 = None

        # 创建主文件夹和子问题文件夹
        self.main_folder = "问题一_统计分析与关系探索"
        self.sub_folders = [
            "1.1_室内温度波动规律统计分析",
            "1.2_室内外温度相关性分析",
            "1.3_热泵能耗与温差定量关系",
            "1.4_室内温度影响因素分析"
        ]

        # 创建文件夹结构
        if not os.path.exists(self.main_folder):
            os.makedirs(self.main_folder)

        for folder in self.sub_folders:
            full_path = os.path.join(self.main_folder, folder)
            if not os.path.exists(full_path):
                os.makedirs(full_path)
                print(f"创建文件夹: {full_path}")

    def load_and_preprocess_data(self):
        """加载和预处理数据"""
        print("正在加载数据...")
        try:
            # 读取建筑1数据
            self.data_building1 = pd.read_csv('../data/附件2/地点1/地点1_完整合并表.csv', encoding='utf-8')
            self.data_building1['建筑标识'] = '建筑1'
            print(f"建筑1数据加载成功，共{len(self.data_building1)}行")

            # 读取建筑2数据
            self.data_building2 = pd.read_csv('../data/附件2/地点2/地点2_完整合并表.csv', encoding='utf-8')
            self.data_building2['建筑标识'] = '建筑2'
            print(f"建筑2数据加载成功，共{len(self.data_building2)}行")

        except Exception as e:
            print(f"数据加载失败: {e}")
            return False

        # 数据预处理
        print("正在进行数据预处理...")
        for data, building in [(self.data_building1, "建筑1"), (self.data_building2, "建筑2")]:
            print(f"\n处理{building}数据:")

            # 转换时间列
            data['时间'] = pd.to_datetime(data['时间'])

            # 检查关键列是否存在
            required_columns = ['时间', '室内温度均值', '环境温度(℃)', '热泵功率(kw)']
            available_columns = [col for col in required_columns if col in data.columns]
            print(f"可用关键列: {available_columns}")

            # 数据清洗
            original_len = len(data)

            # 处理室内温度异常值
            if '室内温度均值' in data.columns:
                data = data[(data['室内温度均值'] >= 10) & (data['室内温度均值'] <= 35)]

            # 处理环境温度异常值
            if '环境温度(℃)' in data.columns:
                data = data[(data['环境温度(℃)'] >= -30) & (data['环境温度(℃)'] <= 50)]

            # 处理热泵功率异常值
            if '热泵功率(kw)' in data.columns:
                data = data[data['热泵功率(kw)'] >= 0]  # 移除负功率值

            # 计算室内外温差
            if '室内温度均值' in data.columns and '环境温度(℃)' in data.columns:
                data['室内外温差'] = data['室内温度均值'] - data['环境温度(℃)']

            # 添加时间特征
            data['小时'] = data['时间'].dt.hour
            data['日期'] = data['时间'].dt.date
            data['月份'] = data['时间'].dt.month
            data['星期几'] = data['时间'].dt.dayofweek

            print(f"{building}预处理完成，从{original_len}行减少到{len(data)}行")

        return True

    def analyze_1_1_temperature_fluctuation(self):
        """1.1 统计所给不同建筑的室内温度波动规律"""
        print("\n=== 1.1 室内温度波动规律统计分析 ===")

        # 创建图表
        fig, axes = plt.subplots(2, 3, figsize=(20, 12))
        fig.suptitle('1.1 不同建筑室内温度波动规律统计分析', fontsize=16, fontweight='bold')

        # 存储分析结果
        analysis_results = {}

        for i, (data, building) in enumerate([(self.data_building1, "建筑1"), (self.data_building2, "建筑2")]):
            if '室内温度均值' not in data.columns:
                continue

            temp_data = data['室内温度均值'].dropna()

            # 描述性统计
            stats_summary = temp_data.describe()

            # 舒适区间分析 (19-21℃)
            comfort_range = (temp_data >= 19) & (temp_data <= 21)
            comfort_rate = comfort_range.sum() / len(temp_data) * 100

            # 存储结果
            analysis_results[building] = {
                'count': len(temp_data),
                'mean': stats_summary['mean'],
                'std': stats_summary['std'],
                'min': stats_summary['min'],
                'max': stats_summary['max'],
                'q25': stats_summary['25%'],
                'median': stats_summary['50%'],
                'q75': stats_summary['75%'],
                'comfort_rate': comfort_rate,
                'range': stats_summary['max'] - stats_summary['min']
            }

            # 1. 时间序列图
            # 为了可视化，每24小时采样一次
            sampled_data = data.set_index('时间').resample('24H')['室内温度均值'].mean()
            axes[i, 0].plot(sampled_data.index, sampled_data.values, linewidth=1, alpha=0.8)
            axes[i, 0].axhline(y=20, color='red', linestyle='--', alpha=0.7, label='目标温度20℃')
            axes[i, 0].axhspan(19, 21, alpha=0.2, color='green', label='舒适区间19-21℃')
            axes[i, 0].set_title(f'{building} - 室内温度时间序列')
            axes[i, 0].set_xlabel('时间')
            axes[i, 0].set_ylabel('室内温度 (℃)')
            axes[i, 0].legend()
            axes[i, 0].grid(True, alpha=0.3)

            # 2. 直方图/核密度估计
            axes[i, 1].hist(temp_data, bins=50, density=True, alpha=0.7, color='skyblue', edgecolor='black')

            # 添加核密度估计
            from scipy.stats import gaussian_kde
            kde = gaussian_kde(temp_data)
            x_range = np.linspace(temp_data.min(), temp_data.max(), 100)
            axes[i, 1].plot(x_range, kde(x_range), 'r-', linewidth=2, label='核密度估计')

            axes[i, 1].axvline(x=temp_data.mean(), color='red', linestyle='--', label=f'均值: {temp_data.mean():.2f}℃')
            axes[i, 1].axvspan(19, 21, alpha=0.2, color='green', label='舒适区间')
            axes[i, 1].set_title(f'{building} - 室内温度分布')
            axes[i, 1].set_xlabel('室内温度 (℃)')
            axes[i, 1].set_ylabel('密度')
            axes[i, 1].legend()
            axes[i, 1].grid(True, alpha=0.3)

            # 3. 日内波动模式
            hourly_temp = data.groupby('小时')['室内温度均值'].agg(['mean', 'std'])
            axes[i, 2].plot(hourly_temp.index, hourly_temp['mean'], 'o-', linewidth=2, markersize=4)
            axes[i, 2].fill_between(hourly_temp.index,
                                  hourly_temp['mean'] - hourly_temp['std'],
                                  hourly_temp['mean'] + hourly_temp['std'],
                                  alpha=0.3)
            axes[i, 2].set_title(f'{building} - 日内温度波动模式')
            axes[i, 2].set_xlabel('小时')
            axes[i, 2].set_ylabel('室内温度 (℃)')
            axes[i, 2].grid(True, alpha=0.3)
            axes[i, 2].set_xticks(range(0, 24, 4))

        plt.tight_layout()

        # 保存图表
        save_path = os.path.join(self.main_folder, self.sub_folders[0], '室内温度波动规律统计分析.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

        # 生成箱形图对比
        self._create_temperature_boxplot(analysis_results)

        # 生成分析报告
        self._generate_1_1_report(analysis_results)

        return analysis_results

    def _create_temperature_boxplot(self, analysis_results):
        """创建温度分布箱形图对比"""
        fig, ax = plt.subplots(1, 1, figsize=(10, 6))

        # 准备箱形图数据
        temp_data_list = []
        labels = []

        for data, building in [(self.data_building1, "建筑1"), (self.data_building2, "建筑2")]:
            if '室内温度均值' in data.columns:
                temp_data_list.append(data['室内温度均值'].dropna())
                labels.append(building)

        # 绘制箱形图
        box_plot = ax.boxplot(temp_data_list, labels=labels, patch_artist=True)

        # 美化箱形图
        colors = ['lightblue', 'lightcoral']
        for patch, color in zip(box_plot['boxes'], colors):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)

        # 添加舒适区间
        ax.axhspan(19, 21, alpha=0.2, color='green', label='舒适区间19-21℃')
        ax.axhline(y=20, color='red', linestyle='--', alpha=0.7, label='目标温度20℃')

        ax.set_title('两栋建筑室内温度分布对比', fontsize=14, fontweight='bold')
        ax.set_ylabel('室内温度 (℃)')
        ax.legend()
        ax.grid(True, alpha=0.3)

        # 保存图表
        save_path = os.path.join(self.main_folder, self.sub_folders[0], '建筑温度分布箱形图对比.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

    def _generate_1_1_report(self, analysis_results):
        """生成1.1分析报告"""
        report_path = os.path.join(self.main_folder, self.sub_folders[0], '室内温度波动规律分析报告.txt')

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("1.1 室内温度波动规律统计分析报告\n")
            f.write("="*60 + "\n\n")

            f.write("一、分析目的\n")
            f.write("-"*30 + "\n")
            f.write("了解两栋建筑在供暖季室内温度的稳定性、范围和分布情况。\n\n")

            f.write("二、所需数据字段\n")
            f.write("-"*30 + "\n")
            f.write("- 时间: 时间戳数据\n")
            f.write("- 室内温度均值: 多个室内采集点的平均温度值\n")
            f.write("- 建筑标识: 用于区分不同建筑的标识符\n\n")

            f.write("三、分析方法\n")
            f.write("-"*30 + "\n")
            f.write("1. 数据预处理: 时间解析、异常值处理、缺失值处理\n")
            f.write("2. 描述性统计: 计算均值、中位数、标准差、四分位数等\n")
            f.write("3. 可视化分析: 时间序列图、直方图、核密度估计、箱形图\n")
            f.write("4. 舒适度分析: 基于19-21℃舒适区间的达标率计算\n\n")

            f.write("四、统计分析结果\n")
            f.write("-"*30 + "\n")

            # 创建对比表格
            f.write("描述性统计对比表:\n")
            f.write(f"{'指标':<15} {'建筑1':<15} {'建筑2':<15} {'单位':<10}\n")
            f.write("-" * 60 + "\n")

            for building, results in analysis_results.items():
                if building == '建筑1':
                    building1_results = results
                else:
                    building2_results = results

            if 'building1_results' in locals() and 'building2_results' in locals():
                f.write(f"{'数据点数':<15} {building1_results['count']:<15} {building2_results['count']:<15} {'个':<10}\n")
                f.write(f"{'平均值':<15} {building1_results['mean']:<15.2f} {building2_results['mean']:<15.2f} {'℃':<10}\n")
                f.write(f"{'标准差':<15} {building1_results['std']:<15.2f} {building2_results['std']:<15.2f} {'℃':<10}\n")
                f.write(f"{'最小值':<15} {building1_results['min']:<15.2f} {building2_results['min']:<15.2f} {'℃':<10}\n")
                f.write(f"{'最大值':<15} {building1_results['max']:<15.2f} {building2_results['max']:<15.2f} {'℃':<10}\n")
                f.write(f"{'中位数':<15} {building1_results['median']:<15.2f} {building2_results['median']:<15.2f} {'℃':<10}\n")
                f.write(f"{'25%分位数':<15} {building1_results['q25']:<15.2f} {building2_results['q25']:<15.2f} {'℃':<10}\n")
                f.write(f"{'75%分位数':<15} {building1_results['q75']:<15.2f} {building2_results['q75']:<15.2f} {'℃':<10}\n")
                f.write(f"{'温度范围':<15} {building1_results['range']:<15.2f} {building2_results['range']:<15.2f} {'℃':<10}\n")
                f.write(f"{'舒适度达标率':<15} {building1_results['comfort_rate']:<15.1f} {building2_results['comfort_rate']:<15.1f} {'%':<10}\n")

            f.write("\n五、关键发现\n")
            f.write("-"*30 + "\n")

            for building, results in analysis_results.items():
                f.write(f"\n{building}分析结果:\n")
                f.write(f"- 温度控制水平: 平均{results['mean']:.2f}℃，标准差{results['std']:.2f}℃\n")
                f.write(f"- 温度稳定性: 波动范围{results['range']:.2f}℃\n")
                f.write(f"- 舒适度表现: {results['comfort_rate']:.1f}%的时间在舒适区间(19-21℃)\n")

                if results['std'] < 1.0:
                    stability = "优秀"
                elif results['std'] < 2.0:
                    stability = "良好"
                else:
                    stability = "一般"
                f.write(f"- 控制稳定性评价: {stability}\n")

            f.write("\n六、生成的图表文件\n")
            f.write("-"*30 + "\n")
            f.write("1. 室内温度波动规律统计分析.png - 包含时间序列、分布和日内波动分析\n")
            f.write("2. 建筑温度分布箱形图对比.png - 两栋建筑温度分布对比\n")

            f.write("\n七、结论与建议\n")
            f.write("-"*30 + "\n")
            f.write("1. 温度控制效果评估: 基于平均值、标准差和舒适度达标率\n")
            f.write("2. 建筑间差异分析: 对比两栋建筑的温度控制性能\n")
            f.write("3. 优化建议: 针对温度波动大的建筑提出控制策略改进建议\n")

        print(f"已生成1.1分析报告: {report_path}")

    def run_complete_analysis(self):
        """运行完整的学术化分析"""
        print("开始问题一：统计分析与关系探索")

        # 1. 加载和预处理数据
        if not self.load_and_preprocess_data():
            return

        # 2. 执行1.1分析
        results_1_1 = self.analyze_1_1_temperature_fluctuation()

        # 3. 执行1.2分析
        results_1_2 = self.analyze_1_2_indoor_outdoor_correlation()

        # 4. 执行1.3分析
        results_1_3 = self.analyze_1_3_energy_temperature_relationship()

        # 5. 执行1.4分析
        results_1_4 = self.analyze_1_4_temperature_influencing_factors()

        print("\n问题一：统计分析与关系探索 - 全部完成！")

    def analyze_1_2_indoor_outdoor_correlation(self):
        """1.2 绘制室内外温度相关性曲线"""
        print("\n=== 1.2 室内外温度相关性分析 ===")

        # 创建图表
        fig, axes = plt.subplots(1, 2, figsize=(16, 6))
        fig.suptitle('1.2 室内外温度相关性分析', fontsize=16, fontweight='bold')

        # 存储分析结果
        correlation_results = {}

        for i, (data, building) in enumerate([(self.data_building1, "建筑1"), (self.data_building2, "建筑2")]):
            if '室内温度均值' not in data.columns or '环境温度(℃)' not in data.columns:
                continue

            # 数据对齐和清洗
            clean_data = data[['室内温度均值', '环境温度(℃)']].dropna()

            if len(clean_data) == 0:
                continue

            indoor_temp = clean_data['室内温度均值']
            outdoor_temp = clean_data['环境温度(℃)']

            # 计算相关系数
            correlation_coef, p_value = pearsonr(outdoor_temp, indoor_temp)

            # 线性回归
            X = outdoor_temp.values.reshape(-1, 1)
            y = indoor_temp.values

            model = LinearRegression()
            model.fit(X, y)
            y_pred = model.predict(X)
            r2 = r2_score(y, y_pred)

            # 存储结果
            correlation_results[building] = {
                'correlation_coef': correlation_coef,
                'p_value': p_value,
                'r2': r2,
                'slope': model.coef_[0],
                'intercept': model.intercept_,
                'data_points': len(clean_data),
                'outdoor_temp_range': (outdoor_temp.min(), outdoor_temp.max()),
                'indoor_temp_range': (indoor_temp.min(), indoor_temp.max())
            }

            # 绘制散点图和回归线
            axes[i].scatter(outdoor_temp, indoor_temp, alpha=0.6, s=15, color='blue')

            # 添加回归线
            x_range = np.linspace(outdoor_temp.min(), outdoor_temp.max(), 100)
            y_range = model.predict(x_range.reshape(-1, 1))
            axes[i].plot(x_range, y_range, 'r-', linewidth=2,
                        label=f'回归线: y={model.coef_[0]:.3f}x+{model.intercept_:.3f}')

            # 添加相关信息
            axes[i].set_title(f'{building}\nr={correlation_coef:.3f}, R²={r2:.3f}, p={p_value:.3e}')
            axes[i].set_xlabel('环境温度 (℃)')
            axes[i].set_ylabel('室内温度 (℃)')
            axes[i].legend()
            axes[i].grid(True, alpha=0.3)

            # 添加理想线（如果室内温度完全跟随室外温度）
            min_temp = min(outdoor_temp.min(), indoor_temp.min())
            max_temp = max(outdoor_temp.max(), indoor_temp.max())
            axes[i].plot([min_temp, max_temp], [min_temp, max_temp],
                        'g--', alpha=0.5, label='理想线(y=x)')
            axes[i].legend()

        plt.tight_layout()

        # 保存图表
        save_path = os.path.join(self.main_folder, self.sub_folders[1], '室内外温度相关性分析.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

        # 生成分析报告
        self._generate_1_2_report(correlation_results)

        return correlation_results

    def _generate_1_2_report(self, correlation_results):
        """生成1.2分析报告"""
        report_path = os.path.join(self.main_folder, self.sub_folders[1], '室内外温度相关性分析报告.txt')

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("1.2 室内外温度相关性分析报告\n")
            f.write("="*60 + "\n\n")

            f.write("一、分析目的\n")
            f.write("-"*30 + "\n")
            f.write("探究室内温度受室外环境温度影响的程度，评估建筑保温性能。\n\n")

            f.write("二、所需数据字段\n")
            f.write("-"*30 + "\n")
            f.write("- 时间: 时间戳数据\n")
            f.write("- 室内温度均值: 室内平均温度\n")
            f.write("- 环境温度(℃): 室外环境温度\n")
            f.write("- 建筑标识: 建筑区分标识\n\n")

            f.write("三、分析方法\n")
            f.write("-"*30 + "\n")
            f.write("1. 数据对齐: 确保室内外温度数据时间点对应\n")
            f.write("2. 散点图分析: 可视化室内外温度关系\n")
            f.write("3. 相关性分析: 计算皮尔逊相关系数\n")
            f.write("4. 回归分析: 建立线性回归模型\n\n")

            f.write("四、统计分析公式\n")
            f.write("-"*30 + "\n")
            f.write("1. 皮尔逊相关系数:\n")
            f.write("   r = Σ[(Xi - X̄)(Yi - Ȳ)] / √[Σ(Xi - X̄)² × Σ(Yi - Ȳ)²]\n")
            f.write("   其中: Xi为室外温度, Yi为室内温度\n\n")
            f.write("2. 线性回归模型:\n")
            f.write("   室内温度 = a × 环境温度 + b\n")
            f.write("   其中: a为斜率, b为截距\n\n")
            f.write("3. 决定系数:\n")
            f.write("   R² = 1 - (SSres / SStot)\n")
            f.write("   其中: SSres为残差平方和, SStot为总平方和\n\n")

            f.write("五、分析结果\n")
            f.write("-"*30 + "\n")

            # 结果对比表
            f.write("相关性分析结果对比:\n")
            f.write(f"{'指标':<20} {'建筑1':<15} {'建筑2':<15} {'说明':<20}\n")
            f.write("-" * 75 + "\n")

            building1_results = correlation_results.get('建筑1', {})
            building2_results = correlation_results.get('建筑2', {})

            if building1_results and building2_results:
                f.write(f"{'数据点数':<20} {building1_results['data_points']:<15} {building2_results['data_points']:<15} {'有效数据量':<20}\n")
                f.write(f"{'相关系数(r)':<20} {building1_results['correlation_coef']:<15.4f} {building2_results['correlation_coef']:<15.4f} {'线性相关强度':<20}\n")
                f.write(f"{'决定系数(R²)':<20} {building1_results['r2']:<15.4f} {building2_results['r2']:<15.4f} {'模型解释度':<20}\n")
                f.write(f"{'p值':<20} {building1_results['p_value']:<15.3e} {building2_results['p_value']:<15.3e} {'统计显著性':<20}\n")
                f.write(f"{'回归斜率':<20} {building1_results['slope']:<15.4f} {building2_results['slope']:<15.4f} {'敏感性系数':<20}\n")
                f.write(f"{'回归截距':<20} {building1_results['intercept']:<15.2f} {building2_results['intercept']:<15.2f} {'基础温度':<20}\n")

            f.write("\n六、回归方程\n")
            f.write("-"*30 + "\n")
            for building, results in correlation_results.items():
                f.write(f"{building}回归方程:\n")
                f.write(f"室内温度 = {results['slope']:.4f} × 环境温度 + {results['intercept']:.2f}\n")
                f.write(f"相关系数: r = {results['correlation_coef']:.4f}\n")
                f.write(f"决定系数: R² = {results['r2']:.4f}\n\n")

            f.write("七、相关性强度解释\n")
            f.write("-"*30 + "\n")
            f.write("相关系数解释标准:\n")
            f.write("- |r| > 0.8: 强相关\n")
            f.write("- 0.5 < |r| ≤ 0.8: 中等相关\n")
            f.write("- 0.3 < |r| ≤ 0.5: 弱相关\n")
            f.write("- |r| ≤ 0.3: 极弱相关或无相关\n\n")

            for building, results in correlation_results.items():
                r = abs(results['correlation_coef'])
                if r > 0.8:
                    strength = "强相关"
                elif r > 0.5:
                    strength = "中等相关"
                elif r > 0.3:
                    strength = "弱相关"
                else:
                    strength = "极弱相关"

                f.write(f"{building}: {strength} (|r|={r:.3f})\n")

            f.write("\n八、建筑保温性能评估\n")
            f.write("-"*30 + "\n")
            f.write("保温性能评估原理:\n")
            f.write("- 相关系数越小，建筑保温性能越好\n")
            f.write("- 斜率越小，室内温度对室外温度变化越不敏感\n")
            f.write("- 理想情况下，室内温度应与室外温度无关(r≈0)\n\n")

            f.write("九、生成的图表文件\n")
            f.write("-"*30 + "\n")
            f.write("1. 室内外温度相关性分析.png - 散点图和回归线分析\n")

            f.write("\n十、结论与建议\n")
            f.write("-"*30 + "\n")
            f.write("1. 建筑保温性能对比: 基于相关系数和回归斜率\n")
            f.write("2. 温控系统效果评估: 室内温度稳定性分析\n")
            f.write("3. 优化建议: 针对高相关性建筑的保温改进建议\n")

        print(f"已生成1.2分析报告: {report_path}")

if __name__ == "__main__":
    analyzer = AcademicHeatPumpAnalyzer()
    analyzer.run_complete_analysis()
