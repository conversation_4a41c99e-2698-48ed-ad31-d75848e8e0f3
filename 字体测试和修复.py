#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字体测试和修复脚本
解决matplotlib中文显示和数学符号显示问题
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import numpy as np

def test_and_fix_fonts():
    """测试和修复字体问题"""
    print("=" * 60)
    print("字体测试和修复程序")
    print("=" * 60)
    
    # 1. 检查系统可用字体
    print("\n1. 检查系统可用的中文字体:")
    font_list = [f.name for f in fm.fontManager.ttflist]
    chinese_fonts = [
        'SimHei',           # 黑体
        'Microsoft YaHei',  # 微软雅黑
        'SimSun',           # 宋体
        'KaiTi',            # 楷体
        'FangSong',         # 仿宋
        'Arial Unicode MS', # Mac系统
        'Noto Sans CJK SC', # Linux系统
        'WenQuanYi Micro Hei', # Linux备用
        'DejaVu Sans'       # 通用备用
    ]
    
    available_fonts = []
    for font in chinese_fonts:
        if font in font_list:
            available_fonts.append(font)
            print(f"  ✓ {font} - 可用")
        else:
            print(f"  ✗ {font} - 不可用")
    
    # 2. 设置最佳字体
    if available_fonts:
        best_font = available_fonts[0]
        plt.rcParams['font.sans-serif'] = [best_font]
        print(f"\n2. 设置字体为: {best_font}")
    else:
        print("\n2. 警告: 未找到中文字体，使用默认字体")
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
    
    # 3. 设置数学符号和负号
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['mathtext.fontset'] = 'stix'
    plt.rcParams['mathtext.default'] = 'regular'
    print("3. 数学符号设置完成")
    
    # 4. 创建测试图表
    print("\n4. 创建测试图表...")
    
    # 生成测试数据
    x = np.linspace(0, 10, 100)
    y1 = np.sin(x) + np.random.normal(0, 0.1, 100)
    y2 = np.cos(x)
    
    # 创建图形
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 左图：中文标题和标签测试
    ax1.scatter(x, y1, alpha=0.6, label='数据点')
    ax1.plot(x, y2, 'r-', linewidth=2, label='拟合线 ($R^2$=0.85)')
    ax1.set_xlabel('环境温度 (℃)', fontsize=12)
    ax1.set_ylabel('室内温度 (℃)', fontsize=12)
    ax1.set_title('中文字体测试图 - 温度相关性分析', fontsize=14)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 添加统计信息文本框
    ax1.text(0.05, 0.95, '相关系数: 0.856\n回归方程: y = 0.123x + 2.456\n$R^2$ = 0.850', 
             transform=ax1.transAxes, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8),
             fontsize=10)
    
    # 右图：数学符号测试
    x2 = np.linspace(-5, 5, 100)
    y3 = x2**2
    y4 = -x2**2 + 10
    
    ax2.plot(x2, y3, 'b-', linewidth=2, label='$y = x^2$')
    ax2.plot(x2, y4, 'g-', linewidth=2, label='$y = -x^2 + 10$')
    ax2.set_xlabel('温差 (℃)', fontsize=12)
    ax2.set_ylabel('功率 (kW)', fontsize=12)
    ax2.set_title('数学符号测试 - $R^2$, $\\alpha$, $\\beta$', fontsize=14)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 添加数学公式
    ax2.text(0.05, 0.95, '数学公式测试:\n$\\alpha = \\frac{\\sum x_i}{n}$\n$\\beta^2 = \\sigma^2$\n$R^2 = 0.95$', 
             transform=ax2.transAxes, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8),
             fontsize=10)
    
    plt.tight_layout()
    
    # 保存测试图
    plt.savefig('字体测试结果.png', dpi=300, bbox_inches='tight')
    print("5. 测试图表已保存为: 字体测试结果.png")
    
    # 显示图表
    plt.show()
    
    print("\n" + "=" * 60)
    print("字体测试完成！")
    print("如果图表中的中文和数学符号显示正常，说明修复成功。")
    print("如果仍有问题，请尝试以下解决方案：")
    print("1. 安装中文字体包")
    print("2. 重启Python环境")
    print("3. 更新matplotlib版本")
    print("=" * 60)

def install_font_guide():
    """字体安装指南"""
    print("\n字体安装指南:")
    print("-" * 40)
    print("Windows系统:")
    print("  1. 下载SimHei.ttf或Microsoft YaHei字体")
    print("  2. 复制到 C:\\Windows\\Fonts\\ 目录")
    print("  3. 重启Python")
    
    print("\nLinux系统:")
    print("  sudo apt-get install fonts-wqy-microhei")
    print("  sudo apt-get install fonts-noto-cjk")
    
    print("\nmacOS系统:")
    print("  系统自带Arial Unicode MS字体")
    print("  或安装Noto Sans CJK字体")

if __name__ == "__main__":
    test_and_fix_fonts()
    install_font_guide()
